# MailBot Current Implementation

## Overview

Each user profile is linked to a specific algorithm version, and the system uses a priority-based rule engine to determine how emails should be labeled and processed.

## Architecture Components

### 1. Rule Engine Architecture

#### Rule Structure

- **Location**: `mailbot/utils/label_engine/rules/base.py`
- **Components**:
  - **Priority**: Lower numbers = higher priority (1 is highest)
  - **Filter Expression**: Logical combination of filters (AND, OR, NOT)
  - **Actions**: List of actions to execute when filter matches

#### RuleDriver

- **Location**: `mailbot/utils/label_engine/rules/rule_driver.py`
- **Purpose**: Orchestrates rule evaluation and execution
- **Methods**:
  - `sort_by_priorities()`: Orders rules by priority
  - `apply_first()`: Executes first matching rule (used in main flow)
  - `apply_all()`: Executes all matching rules

### 2. Filter System

#### Base Filter Types

- **Location**: `mailbot/utils/label_engine/filters/base.py`
- **Types**:
  - `AllFilter`: Logical AND of multiple filters
  - `AnyFilter`: Logical OR of multiple filters
  - `NegateFilter`: Logical NOT of a filter

#### Core Filters

1. **InternalMessageFilter**: Identifies internal system messages
2. **UserDefinedActionFilter**: User-created custom rules
3. **LuceneFilter**: Advanced text-based filtering
4. **SameThreadFilter**: Thread-based message grouping
5. **UserTrainingFilter**: User-trained sender preferences
6. **SentMessageFilter**: Identifies sent messages
7. **DomainTrainingFilter**: Domain-based whitelisting
8. **ExceptionListFilter**: Exception handling
9. **FirstTimeSenderFilter**: New sender detection
10. **ReadFractionFilter**: Historical engagement analysis

### 3. Action System

#### Core Actions

1. **InternalMessageAction**: Handles system messages
2. **UserDefinedAction**: Executes custom user actions
3. **SetLabelAction**: Assigns specific labels
4. **SameThreadAction**: Manages thread-level labeling
5. **FirstTimeSenderAction**: New sender treatment
6. **ReadFractionAction**: Engagement-based labeling
7. **DefaultAction**: Fallback action

## Message Processing Flow

### 1. Main Processing Pipeline

```md
Incoming Message
    ↓
Message Parsing (ParsedMessage)
    ↓
Category Analysis (ML/NLP)
    ↓
Rule Engine Processing
    ↓
Label Assignment
    ↓
Action Execution
    ↓
Post-Processing
```

#### Processing Steps

1. **Category Retrieval**: Fetch ML-generated message categories
2. **Rule Evaluation**: Apply main rule set via RuleDriver
3. **Pre-Move Actions**: Execute actions before label assignment
4. **Label Assignment**: Move message to determined label
5. **Post-Move Actions**: Execute follow-up actions
6. **Secondary Account Handling**: Forward if needed

## Algorithm Versions

### Version 1 (Legacy)

- **Logic**: Simple read fraction thresholds
  - ≥90% read rate → WHITE_LIST
  - ≥30% read rate → ZAPPED
  - <30% read rate → ZAPPED (no digest)

### Version 2 (Intermediate)

- **Logic**: Category importance + read fraction
  - <25% open rate: ZAPPED (digest based on importance)
  - 25-80% open rate: INBOX if important, else ZAPPED
  - ≥80% open rate: INBOX if important, else ZAPPED with digest

### Version 3 (Current)

- **Logic**: Category importance + read fraction
  - Score ≤1: ZAPPED
  - Score 2-3: Check read fraction (≥50% → INBOX, <50% → ZAPPED)
  - Score ≥4: INBOX
  - Special handling for "Critical Account/Security Alert" categories

## Rule Priority System

### Main Rule Set (Priority Order)

1. **Internal Messages** (Priority 1)
2. **User Defined Actions** (Priority 2)
3. **Lucene Filters** (Priority 3)
4. **Same Thread** (Priority 4)
5. **User Training** (Priority 5)
6. **Sent Messages** (Priority 6)
7. **Domain Training** (Priority 7)
8. **Exception List** (Priority 8)
9. **First Time Sender** (Priority 9)
10. **Read Fraction** (Priority 10)
11. **Default Action** (Priority 11)

### Same Thread Sub-Rules (Used in SameThreadAction)

1. **User Training** (Priority 1)
2. **Domain Training** (Priority 2)
3. **Exception List** (Priority 3)
4. **First Time Sender** (Priority 4)
5. **Read Fraction** (Priority 5)
6. **Default** (Priority 6)

## Current Implementation Issues

### 1. Algorithm Version Coupling

- Algorithm logic is scattered across multiple action classes
- Version-specific code is embedded within actions
- Adding new algorithms requires modifying existing action classes

### 2. Hard-coded Rule Structure

- Main rule set is statically defined in `__init__.py`
- No dynamic rule configuration
- Difficult to A/B test different rule combinations

### 3. Tight Coupling Between Components

- Filters and actions are tightly coupled to specific algorithm versions
- Message categories are handled differently across versions
- Scoring logic is embedded within actions

### 4. Limited Extensibility

- Adding new message categories requires code changes
- New scoring mechanisms need action modifications
- Filter logic is not easily composable

### 5. Algorithm Selection Logic

- Version selection is done via if/else statements in actions
- No centralized algorithm registry or factory pattern
- Difficult to maintain and extend

## Data Flow Diagrams

### High-Level Message Processing Flow

```md
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Email         │    │   Message       │    │   Category      │
│   Received      │───▶│   Parsing       │───▶│   Analysis      │
│   (Webhook)     │    │   (ParsedMsg)   │    │   (ML/NLP)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Post-Move     │    │   Label         │    │   Rule Engine   │
│   Actions       │◀───│   Assignment    │◀───│   Processing    │
│   (Overlays)    │    │   (Move Email)  │    │   (Filters)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Rule Engine Processing Flow

```md
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   RuleDriver    │    │   Sort Rules    │    │   Evaluate      │
│   Initialize    │───▶│   by Priority   │───▶│   First Rule    │
│   (State+Rules) │    │   (1=Highest)   │    │   Filter        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Filter        │
                                               │   Matches?      │
                                               └─────────────────┘
                                                    │       │
                                                   Yes      No
                                                    │       │
                                                    ▼       ▼
                                     ┌─────────────────┐ ┌─────────────────┐
                                     │   Execute       │ │   Try Next      │
                                     │   Actions       │ │   Rule          │
                                     │   (Return)      │ │   (Loop)        │
                                     └─────────────────┘ └─────────────────┘
```

### Algorithm Version Selection Flow

```md
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ParsedMessage │    │   Get User      │    │   Check         │
│   + Categories  │───▶│   Profile        │───▶│   algo_version  │
│   + Profile      │    │   algo_version  │    │   Field         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Route to      │
                                               │   Algorithm     │
                                               │   Logic         │
                                               └─────────────────┘
                                                        │
                        ┌───────────────────────────────┼───────────────────────────────┐
                        │                               │                               │
                        ▼                               ▼                               ▼
               ┌─────────────────┐            ┌─────────────────┐            ┌─────────────────┐
               │   Algorithm V1  │            │   Algorithm V2  │            │   Algorithm V3  │
               │   (Read Frac    │            │   (Category +   │            │   (Score-based  │
               │   Thresholds)   │            │   Read Frac)    │            │   Classification)│
               └─────────────────┘            └─────────────────┘            └─────────────────┘
                        │                               │                               │
                        └───────────────────────────────┼───────────────────────────────┘
                                                        ▼
                                               ┌─────────────────┐
                                               │   Return Label  │
                                               │   Decision      │
                                               │   + Metadata    │
                                               └─────────────────┘
```

### Current Rule Priority Execution

```md
Priority 1: Internal Messages ────┐
Priority 2: User Defined Actions ──┤
Priority 3: Lucene Filters ───────┤
Priority 4: Same Thread ──────────┤
Priority 5: User Training ────────┤ ──▶ First Match Wins
Priority 6: Sent Messages ────────┤     (apply_first)
Priority 7: Domain Training ──────┤
Priority 8: Exception List ───────┤
Priority 9: First Time Sender ────┤
Priority 10: Read Fraction ───────┤
Priority 11: Default Action ──────┘
```

### Same Thread Sub-Rule Processing

```md
When SameThreadFilter matches:
┌─────────────────┐
│   Create Sub-   │
│   Rule Set      │
│   (6 rules)     │
└─────────────────┘
         │
         ▼
Priority 1: User Training ────┐
Priority 2: Domain Training ──┤
Priority 3: Exception List ───┤ ──▶ First Match Wins
Priority 4: First Time Sender ┤     (apply_first)
Priority 5: Read Fraction ────┤
Priority 6: Default Action ───┘
```
