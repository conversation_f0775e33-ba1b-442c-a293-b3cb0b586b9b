"""
Django management command to test the new algorithm system.

This command helps validate the new algorithm implementation by:
1. Testing algorithm registration and configuration
2. Comparing results between old and new systems
3. Running performance benchmarks
4. Validating configuration files
"""

import time
import logging
from typing import Dict, Any, List
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from mailbot.algorithms.registry import get_registry
from mailbot.algorithms.engine import get_engine
from mailbot.algorithms.types import MessageContext
from mailbot.models import UserMailBotProfile, Message, MessageCategoryThrough
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
from mailbot.config.loader import get_config_loader

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Management command for testing the algorithm system."""

    help = "Test and validate the new algorithm system"

    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            "--test-type",
            type=str,
            choices=["registry", "comparison", "performance", "config", "rules", "enhanced"],
            default="registry",
            help="Type of test to run",
        )

        parser.add_argument(
            "--algorithm-version", type=str, choices=["v1", "v2", "v3"], help="Specific algorithm version to test"
        )

        parser.add_argument(
            "--message-count",
            type=int,
            default=10,
            help="Number of messages to test (for comparison and performance tests)",
        )

        parser.add_argument("--user-id", type=int, help="Specific user ID to test")

        parser.add_argument("--verbose", action="store_true", help="Enable verbose output")

    def handle(self, *args, **options):
        """Handle the command execution."""
        if options["verbose"]:
            logging.basicConfig(level=logging.DEBUG)

        test_type = options["test_type"]

        try:
            if test_type == "registry":
                self.test_algorithm_registry(options)
            elif test_type == "comparison":
                self.test_algorithm_comparison(options)
            elif test_type == "performance":
                self.test_performance(options)
            elif test_type == "config":
                self.test_configuration(options)
            elif test_type == "rules":
                self.test_rule_engine(options)
            elif test_type == "enhanced":
                self.test_enhanced_features(options)

            self.stdout.write(self.style.SUCCESS(f"Successfully completed {test_type} test"))

        except Exception as e:
            logger.exception(f"Test failed: {e}")
            raise CommandError(f"Test failed: {e}")

    def test_algorithm_registry(self, options):
        """Test algorithm registry functionality."""
        self.stdout.write("Testing algorithm registry...")

        registry = get_registry()

        # Test available algorithms
        versions = registry.list_versions()
        self.stdout.write(f"Available algorithms: {versions}")

        # Test each algorithm
        for version in versions:
            if options.get("algorithm_version") and version != options["algorithm_version"]:
                continue

            self.stdout.write(f"\nTesting algorithm {version}:")

            try:
                algorithm = registry.get_algorithm(version)
                self.stdout.write(f"  ✓ Algorithm instance created")

                # Test configuration schema
                schema = algorithm.get_configuration_schema()
                self.stdout.write(f"  ✓ Configuration schema available")

                # Test configuration validation
                config = registry.get_config(version)
                if config:
                    is_valid = algorithm.validate_config(config)
                    self.stdout.write(f"  ✓ Configuration valid: {is_valid}")
                else:
                    self.stdout.write(f"  ! No configuration found")

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Error testing {version}: {e}"))

    def test_algorithm_comparison(self, options):
        """Test comparison between old and new algorithm systems."""
        self.stdout.write("Testing algorithm comparison...")

        # Get test messages
        messages = self._get_test_messages(options)

        if not messages:
            self.stdout.write(self.style.WARNING("No test messages found"))
            return

        self.stdout.write(f"Testing {len(messages)} messages...")

        matches = 0
        total = 0
        differences = []

        for message in messages:
            try:
                # Get message data
                parsed_message = self._create_parsed_message(message)
                profile = message.user_mailbot_profile
                categories = self._get_message_categories(message)

                # Test legacy system
                legacy_result = self._run_legacy_algorithm(parsed_message, profile, categories)

                # Test new system
                new_result = self._run_new_algorithm(parsed_message, profile, categories)

                # Compare results
                labels_match = legacy_result.get("label_name") == new_result.get("label_name")

                if labels_match:
                    matches += 1
                else:
                    differences.append(
                        {
                            "message_id": message.message_id,
                            "legacy_label": legacy_result.get("label_name"),
                            "new_label": new_result.get("label_name"),
                            "legacy_reason": legacy_result.get("message_labelled_due_to"),
                            "new_reason": new_result.get("message_labelled_due_to"),
                        }
                    )

                total += 1

                if options["verbose"]:
                    self.stdout.write(
                        f"Message {message.message_id}: "
                        f"Legacy={legacy_result.get('label_name')}, "
                        f"New={new_result.get('label_name')}, "
                        f"Match={labels_match}"
                    )

            except Exception as e:
                logger.warning(f"Failed to test message {message.message_id}: {e}")

        # Report results
        accuracy = (matches / total * 100) if total > 0 else 0
        self.stdout.write(f"\nComparison Results:")
        self.stdout.write(f"  Total messages tested: {total}")
        self.stdout.write(f"  Matching results: {matches}")
        self.stdout.write(f"  Accuracy: {accuracy:.1f}%")

        if differences and options["verbose"]:
            self.stdout.write(f"\nDifferences:")
            for diff in differences[:10]:  # Show first 10 differences
                self.stdout.write(f"  {diff['message_id']}: " f"{diff['legacy_label']} -> {diff['new_label']}")

    def test_performance(self, options):
        """Test performance of new algorithm system."""
        self.stdout.write("Testing algorithm performance...")

        messages = self._get_test_messages(options)

        if not messages:
            self.stdout.write(self.style.WARNING("No test messages found"))
            return

        # Test legacy system performance
        start_time = time.time()
        for message in messages:
            try:
                parsed_message = self._create_parsed_message(message)
                profile = message.user_mailbot_profile
                categories = self._get_message_categories(message)
                self._run_legacy_algorithm(parsed_message, profile, categories)
            except Exception:
                pass
        legacy_time = time.time() - start_time

        # Test new system performance
        start_time = time.time()
        for message in messages:
            try:
                parsed_message = self._create_parsed_message(message)
                profile = message.user_mailbot_profile
                categories = self._get_message_categories(message)
                self._run_new_algorithm(parsed_message, profile, categories)
            except Exception:
                pass
        new_time = time.time() - start_time

        # Report results
        self.stdout.write(f"\nPerformance Results:")
        self.stdout.write(f"  Messages tested: {len(messages)}")
        self.stdout.write(f"  Legacy system time: {legacy_time:.3f}s")
        self.stdout.write(f"  New system time: {new_time:.3f}s")
        self.stdout.write(f"  Performance ratio: {new_time/legacy_time:.2f}x")

        if new_time < legacy_time:
            self.stdout.write(self.style.SUCCESS("  ✓ New system is faster"))
        else:
            self.stdout.write(self.style.WARNING("  ! New system is slower"))

    def test_configuration(self, options):
        """Test configuration loading and validation."""
        self.stdout.write("Testing configuration system...")

        loader = get_config_loader()

        # Test available configurations
        configs = loader.list_available_configs()
        self.stdout.write(f"Available configurations: {configs}")

        # Test each configuration
        for config_name in configs:
            if options.get("algorithm_version") and config_name != options["algorithm_version"]:
                continue

            self.stdout.write(f"\nTesting configuration {config_name}:")

            try:
                config_data = loader.load_algorithm_config(config_name)
                self.stdout.write(f"  ✓ Configuration loaded")

                # Validate required sections
                required_sections = ["thresholds", "scoring_rules"]
                for section in required_sections:
                    if section in config_data:
                        self.stdout.write(f"  ✓ {section} section present")
                    else:
                        self.stdout.write(self.style.WARNING(f"  ! {section} section missing"))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Error loading {config_name}: {e}"))

    def _get_test_messages(self, options) -> List[Message]:
        """Get test messages for comparison."""
        queryset = Message.objects.select_related("user_mailbot_profile")

        if options.get("user_id"):
            queryset = queryset.filter(user_mailbot_profile__user_id=options["user_id"])

        # Get recent messages with categories
        queryset = queryset.filter(categories__isnull=False).distinct().order_by("-created")

        return list(queryset[: options["message_count"]])

    def _create_parsed_message(self, message: Message) -> ParsedMessage:
        """Create ParsedMessage from Message model."""
        # This is a simplified version - in practice you'd need to reconstruct
        # the full ParsedMessage from the stored message data
        parsed_message = ParsedMessage()
        parsed_message.message_id = message.message_id
        parsed_message.user_mailbot_profile_id = message.user_mailbot_profile_id
        parsed_message.from_name_email = ("", message.sender_email or "")
        parsed_message.subject = message.subject or ""
        return parsed_message

    def _get_message_categories(self, message: Message) -> List:
        """Get categories for a message."""
        through_instances = MessageCategoryThrough.objects.filter(message=message).select_related("category")
        return [instance.category for instance in through_instances]

    def _run_legacy_algorithm(self, parsed_message, profile, categories) -> Dict[str, Any]:
        """Run legacy algorithm system."""
        return ReadFractionAction.base_action(
            sender_profile=None,  # Would need to get this
            algo_version=profile.algo_version,
            parsed_message=parsed_message,
            message_categories=categories,
        )

    def _run_new_algorithm(self, parsed_message, profile, categories) -> Dict[str, Any]:
        """Run new algorithm system."""
        engine = get_engine()
        return engine.process_message_legacy(parsed_message, profile, categories)

    def test_rule_engine(self, options):
        """Test enhanced rule engine functionality."""
        self.stdout.write("Testing enhanced rule engine...")

        try:
            from mailbot.rules.engine import get_enhanced_rule_engine
            from mailbot.rules.loader import get_rule_loader

            engine = get_enhanced_rule_engine()
            loader = get_rule_loader()

            self.stdout.write(f"✓ Enhanced rule engine created: {type(engine).__name__}")

            # Test available rule sets
            rule_sets = loader.list_available_rule_sets()
            self.stdout.write(f"✓ Available rule sets: {rule_sets}")

            # Test each algorithm version
            for version in ["v1", "v2", "v3"]:
                if options.get("algorithm_version") and version != options["algorithm_version"]:
                    continue

                self.stdout.write(f"\nTesting rules for {version}:")

                try:
                    # Test rule validation
                    validation = engine.validate_rules(version, "main")
                    if validation["valid"]:
                        self.stdout.write(f"  ✓ Rules valid: {validation['rule_count']} rules")
                    else:
                        self.stdout.write(f"  ! Rules invalid: {validation['errors']}")

                    # Test rule info
                    info = engine.get_rule_info(version, "main")
                    self.stdout.write(f"  ✓ Rule info: {info['rule_count']} rules loaded")

                    if options["verbose"]:
                        for i, rule in enumerate(info["rules"]):
                            self.stdout.write(
                                f"    Rule {i+1}: Priority {rule['priority']}, "
                                f"Filter: {rule['filter_type']}, "
                                f"Actions: {rule['action_count']}"
                            )

                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  ✗ Error testing rules for {version}: {e}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Rule engine test failed: {e}"))

    def test_enhanced_features(self, options):
        """Test enhanced features like filters and actions."""
        self.stdout.write("Testing enhanced features...")

        try:
            from mailbot.rules.factory import get_filter_factory, get_action_factory
            from mailbot.rules.filters import ScoreThresholdFilter, CategoryFilter
            from mailbot.rules.actions import ParameterizedSetLabelAction

            # Test filter factory
            filter_factory = get_filter_factory()
            self.stdout.write(f"✓ Filter factory created")

            available_filters = filter_factory.list_filters()
            self.stdout.write(f"✓ Available filters: {len(available_filters)}")

            if options["verbose"]:
                for filter_name in available_filters[:10]:  # Show first 10
                    self.stdout.write(f"    - {filter_name}")

            # Test action factory
            action_factory = get_action_factory()
            self.stdout.write(f"✓ Action factory created")

            available_actions = action_factory.list_actions()
            self.stdout.write(f"✓ Available actions: {len(available_actions)}")

            if options["verbose"]:
                for action_name in available_actions[:10]:  # Show first 10
                    self.stdout.write(f"    - {action_name}")

            # Test enhanced filter creation
            try:
                score_filter = ScoreThresholdFilter(threshold=2.0, operator=">=")
                self.stdout.write("✓ ScoreThresholdFilter created")

                category_filter = CategoryFilter(category_names=["Test"])
                self.stdout.write("✓ CategoryFilter created")

                # Test filter from config
                filter_config = {"type": "ScoreThresholdFilter", "params": {"threshold": 3.0, "operator": ">="}}
                config_filter = filter_factory.create_filter(filter_config)
                self.stdout.write("✓ Filter created from config")

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Enhanced filter test failed: {e}"))

            # Test enhanced action creation
            try:
                action = ParameterizedSetLabelAction(label_name="TEST_LABEL", reason="TEST_REASON")
                self.stdout.write("✓ ParameterizedSetLabelAction created")

                # Test action from config
                action_config = {
                    "type": "ParameterizedSetLabelAction",
                    "params": {"label_name": "CONFIG_LABEL", "reason": "CONFIG_REASON"},
                }
                config_action = action_factory.create_action(action_config)
                self.stdout.write("✓ Action created from config")

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Enhanced action test failed: {e}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Enhanced features test failed: {e}"))
