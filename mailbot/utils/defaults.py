from enum import Enum


class MailBotMessageHeaders(Enum):
    """Message headers used in mailbot sent emails and used to identify them when received events on webhook."""

    # Set for all emails
    MESSAGE_CATEGORY = "X-MailBot-Message-Category"
    # Set for all emails that are sent following user's message
    ORIGINAL_MESSAGE_ID = "X-MailBot-Original-Message-ID"
    MAILBOT_PROFILE_ID = "X-MailBot-Profile-ID"
    # Set for all emails that are forwarded from secondary account
    SECONDARY_ACCOUNT_EMAIL = "X-MailBot-Secondary-Account-Email"


class MailBotTemplateTag(Enum):
    """
    Email template tags for mailbot.
    """

    WELCOME = "welcome"
    FIRST_TIME_SENDER_OVERLAY = "first_time_sender_overlay"
    DIGEST_ZAPPED = "digest_zapped"
    AUTO_RESPONDER = "auto_responder"
    LUCENE_ALERT = "lucene_alert"
    REAUTHORIZATION_DEV = "reauthorization_dev"
    # Reminder email for reauthorization
    REAUTHORIZATION_REMINDER_PROD = "reauthorization_reminder_prod"
    # Post expiry email for reauthorization
    REAUTHORIZATION_REMINDER_POST_EXPIRY_PROD = "reauthorization_reminder_post_expiry_prod"
    REACTIVATE_REMINDER = "reactivate_reminder"
    RECREATE_LABEL_ALERT = "recreate_label_alert"
    UNSUBSCRIBE_SENDER_MAIL = "unsubscribe_sender_mail"
    SECONDARY_PROFILE_DIGEST = "secondary_profile_digest"
    FIRST_TIME_SENDER_OVERLAY_TO_PRIMARY = "first_time_sender_overlay_to_primary"
    FORWARD_INBOX_SECONDARY_TO_PRIMARY = "forward_inbox_secondary_to_primary"
    # Inbox overlay email labels
    OVERLAY_DO_NOW = "overlay_do_now"
    OVERLAY_SCHEDULE = "overlay_schedule"
    OVERLAY_DELEGATE = "overlay_delegate"
    OVERLAY_ELIMINATE = "overlay_eliminate"


class MailBotMessageCategory(Enum):
    """
    Message category names that must be attached in email's header sent by mailbot.
    We use multiple categories for same email templates, different context to distinguish purpose of email.
    """

    WELCOME = "welcome"
    FIRST_TIME_SENDER_OVERLAY = "first_time_sender_overlay"
    DIGEST = "digest"
    AUTO_RESPONDER = "auto_responder"
    LUCENE_ALERT = "lucene_alert"
    REAUTHORIZATION_DEV = "reauthorization_dev"
    REAUTHORIZATION_REMINDER_PROD = "reauthorization_reminder_prod"
    REAUTHORIZATION_REMINDER_POST_EXPIRY_PROD = "reauthorization_reminder_post_expiry_prod"
    REACTIVATE_REMINDER = "reactivate_reminder"
    RECREATE_LABEL_ALERT = "recreate_label_alert"
    FEEDBACK = "feedback"
    INSUFFICIENT_PERMISSIONS = "insufficient_permissions"
    TRIAL_EXPIRY = "trial_expiry"
    CREDIT_CARD_MISSING = "credit_card_missing"
    FORWARD_INBOX_SECONDARY_TO_PRIMARY = "forward_inbox_secondary_to_primary"
    FORWARD_FIRST_TIME_SENDER_OVERLAY_TO_PRIMARY = "forward_first_time_sender_overlay_to_primary"
    INBOX_OVERLAY = "inbox_overlay"


OVERLAY_MESSAGE_CATEGORIES = [
    MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY.value,
    MailBotMessageCategory.LUCENE_ALERT.value,
    MailBotMessageCategory.INBOX_OVERLAY.value,
]

FORWARDED_OVERLAY_MESSAGE_CATEGORIES = [
    MailBotMessageCategory.FORWARD_INBOX_SECONDARY_TO_PRIMARY.value,
    MailBotMessageCategory.FORWARD_FIRST_TIME_SENDER_OVERLAY_TO_PRIMARY.value,
]


class MailBotMessageLabelReason(Enum):
    INTERNAL_MESSAGE_FILTER = "internal_message_filter"
    LUCENE_FILTER = "lucene_filter"
    SAME_THREAD_FILTER = "same_thread_filter"
    USER_TRAINING_FILTER = "user_training_filter"
    DOMAIN_TRAINING_FILTER = "domain_training_filter"
    EXCEPTION_LIST = "exception_list"
    FIRST_TIME_SENDER_FILTER = "first_time_sender_filter"
    SENDER_PROFILE_FILTER = "sender_profile_filter"
    READ_FRACTION_FILTER = "read_fraction_filter"
    NO_FILTER = "no_filter"
    CALENDAR_INVITE_FILTER = "calendar_invite_filter"
    SENT_MESSAGE_FILTER = "sent_message_filter"
    UNSUBSCRIBED_FILTER = "unsubscribed_filter"
    DEFAULT_ACTION = "default_action"


class MailBotScheduledTasks(Enum):
    """
    Mailbot scheduled task tags that must be used with `ScheduledTask` tags.
    """

    AUTO_ARCHIVER = "mailbot.tasks.archive_scheduled_auto_archival_mail"
    SEND_AUTO_RESPONDER_MAIL = "mailbot.tasks.send_scheduled_auto_responder_mail"
    SEND_REACTIVATE_REMINDER_MAIL = "mailbot.tasks.send_reactivate_reminder_mail"
    SEND_REAUTHORIZATION_DEV_MAIL = "mailbot.tasks.send_reauthorization_mails_dev"
    SEND_REAUTHORIZATION_PROD_MAIL = "mailbot.tasks.send_reauthorization_mail_prod"
    SEND_FEEDBACK_MAIL = "mailbot.tasks.send_feedback_mail"
    SEND_MAIL = "mailbot.tasks.send_mail"
    UPDATE_LABELS = "mailbot.tasks.update_labels"


class MailOperationType(Enum):
    TRAINING_THROUGH_FIRST_TIME_SENDER = 1
    TRAINING_THROUGH_DIGEST = 2
    DOMAIN_TRAINING_THROUGH_FIRST_TIME_SENDER = 3
    PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER = 4
    CANCEL_LUCENE_ALERT = 5
    CANCEL_LUCENE_ALERT_FOR_SENDER = 6
    CANCEL_LUCENE_ALERT_FOR_DOMAIN = 7


class MailOperationStatus(Enum):
    ORIGINAL_MESSAGE_NOT_FOUND = 1
    MESSAGE_ALREADY_MOVED_TO_LABEL = 2
    PREFERENCE_ALREADY_UPDATED = 3
    ORIGINAL_MESSAGE_IN_TRASH = 4
    MESSAGE_MOVED_SUCCESS = 5
    DOMAIN_TRAINING_SUCCESS = 6
    UNDO_DOMAIN_TRAINING_SUCCESS = 7
    USER_TRAINING_SUCCESS = 8
    UNDO_USER_TRAINING_SUCCESS = 9
    DOMAIN_PREFERENCE_UPDATE_SUCCESS = 10
    UNDO_DOMAIN_PREFERENCE_UPDATE_SUCCESS = 11
    SENDER_AUTO_ARCHIVE_PREFERENCE_UPDATE_SUCCESS = 12
    DOMAIN_AUTO_ARCHIVE_PREFERENCE_UPDATE_SUCCESS = 13
    OPERATION_NOT_SUPPORTED = 14
    AUTO_ARCHIVE_CANCEL_SUCCESS = 15


class GmailKnownLabelName(Enum):
    SENT = "SENT"
    TRASH = "TRASH"
    SPAM = "SPAM"
    DRAFT = "DRAFT"
    INBOX = "INBOX"
    UNREAD = "UNREAD"
    STARRED = "STARRED"
    IMPORTANT = "IMPORTANT"
    CATEGORY_FORUMS = "CATEGORY_FORUMS"
    CATEGORY_SOCIAL = "CATEGORY_SOCIAL"
    CATEGORY_UPDATES = "CATEGORY_UPDATES"
    CATEGORY_PERSONAL = "CATEGORY_PERSONAL"
    CATEGORY_PROMOTIONS = "CATEGORY_PROMOTIONS"


class OutlookKnownLabelName(Enum):
    SENT = "Sent Items"
    TRASH = "Deleted Items"
    INBOX = "Inbox"
    DRAFT = "Drafts"
    ARCHIVE = "Archive"
    SPAM = "Junk Email"


class MailBotInsightsSlackMessageFormat(Enum):
    EXCEPTION_LIST_ADDRESS_TRAINED = (
        "User {user_email} trained the exception list {exception_type}: {exception_address} to {user_training}"
    )
    ONBOARDING_FAILED = "Onboarding failed for user {user_email}"


class GoogleAdsConversionRecordsSlackMessageFormat(Enum):
    GOOGLE_ADS_CLICK_CONVERSION_RECORDS = "Uploaded a total of {results} google ads click conversion records."


class FirstTimeSenderTreatment(Enum):
    LOW_PRIORITY_WITH_OVERLAY = "low_priority_with_overlay"
    LOW_PRIORITY = "low_priority"
    INBOX = "inbox"


class MailBotProfileMetadataKey(Enum):
    HISTORY_ID = "history_id"
    NEW_UI_VIEWED = "new_ui_viewed"
    LAST_ONBOARDING_TOUR_STEP_VIEWED = "last_onboarding_tour_step_viewed"
    ONBOARDING_COMPLETED = "onboarding_completed"
    ONBOARDING_COMPLETED_AT = "onboarding_completed_at"
    ONBOARDING_FAILED = "onboarding_failed"
    ONBOARDING_FAILED_AT = "onboarding_failed_at"
    LAST_DISABLED_AT = "last_disabled_at"
    REFRESH_TOKEN_EXPIRED = "refresh_token_expired"
    REFRESH_TOKEN_EXPIRED_AT = "refresh_token_expired_at"
    FEEDBACK_MAIL_SENT = "feedback_mail_sent"
    FEEDBACK_MAIL_SENT_AT = "feedback_mail_sent_at"
    LAST_EVENT_PROCESSED_AT = "last_event_processed_at"
    ONBOARDING_STARTED_AT = "onboarding_started_at"
    TRACKING_PARAMETERS = "tracking_parameters"
    LAST_ENABLED_AT = "last_enabled_at"
    ALL_SCOPES_GRANTED = "all_scopes_granted"
    ALL_SCOPES_GRANTED_AT = "all_scopes_granted_at"
    STRIPE_PAYMENT_PLAN = "stripe_payment_plan"
    NON_GRANTED_SCOPES = "non_granted_scopes"
    USER_ROLE = "user_role"
    LAST_CHECKOUT_SESSION_CREATED_AT = "last_checkout_session_created_at"
    GOOGLE_ADS_CLICK_CONVERSION_SYNCED = "google_ads_click_conversion_synced"


MAILBOT_DEFAULT_PREFERENCES = {
    "mailbot_enabled": False,
    "digest_enabled": True,
    "ai_auto_responder_enabled": True,
    "first_time_sender_treatment": "low_priority_with_overlay",
    "first_time_sender_overlay_limit": "5",
    "digest_time": "12:00",
    "archive_mailbot_mails_enabled": True,
    "archive_mailbot_mails_after": "24",
    "label_engine_enabled": True,
}
AUTO_RESPONDER_MAIL_VERIFICATION_URL = (
    "https://emailzap.co/human-confirmation?utm_source=Verification-page&utm_medium=signup"
)
MICROSOFT_MAILBOT_REQUIRED_SCOPES = ["Mail.ReadWrite", "User.Read", "Mail.Send"]
GOOGLE_MAILBOT_REQUIRED_SCOPES = [
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/gmail.labels",
    "https://www.googleapis.com/auth/gmail.modify",
    "openid",
]
JARVIS_GOOGLE_AUTH_REQUIRED_SCOPES = [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive.file",
    "https://www.googleapis.com/auth/userinfo.email",
]
MICROSOFT_EXTENDED_PROPERTY_ID = "ec17fdee-3015-498e-a943-95e5bd989739"
MICROSOFT_EXTENDED_PROPERTY_NAME = "bot_assigned_label"

# Statistics Cache Keys
PROCESSED_COUNT = "PROCESSED_COUNT"
MOVED_COUNT = "MOVED_COUNT"

GMAIL_ACCESS_REVOKED_AFTER_DAYS = 180

# Reauthorization Reminder Email Schedule
# Key: order of reauthorization reminder email
# Value: number of days before reauthorization to send the reminder email (negative values for after expiry)
REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE = {
    1: 30,
    2: 15,
    3: 7,
    4: 6,
    5: 5,
    6: 4,
    7: 3,
    8: 2,
    9: 1,
    10: -1,
    11: -2,
    12: -3,
}

MAILBOT_INSIGHTS_SLACK_CHANNEL_ID = "C07NTQHPWUQ"
OUTLOOK_LABELS = {"white_list": "Inbox", "zapped": "Zapped"}
GMAIL_LABELS = {"white_list": "INBOX", "zapped": "-Zapped"}

PDF_MIME_TYPE = "application/pdf"
DOCX_MIME_TYPE = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

GOOGLE_ADS_CONVERSION_RECORDS_SLACK_CHANNEL_ID = "C08RG7FE29Y"


class SubscriptionStatisticsKey(Enum):
    SENDERS_UNSUBSCRIBED = "senders_unsubscribed"
    SENDERS_TRASHED = "senders_trashed"


class AnalyticsStatisticsKey(Enum):
    SENDERS_UNSUBSCRIBED = "senders_unsubscribed"
    TRASHED_COUNT = "trashed_count"
    MESSAGES_SCANNED = "messages_scanned"
    MESSAGES_ZAPPED = "messages_zapped"


# Inbox overlay ENUMS
class InboxOverlayLabel(Enum):
    DO_NOW = "Do now"
    SCHEDULE = "Schedule"
    DELEGATE = "Delegate"
    ELIMINATE = "Eliminate"

    @staticmethod
    def is_valid_label(label):
        if not label:
            return False
        return label in [member.value for member in InboxOverlayLabel]


class ActionTypes(Enum):
    DELETE = "delete"
    UNSUBSCRIBE = "unsubscribe"
    MANAGE_SENDER = "manage_sender"
