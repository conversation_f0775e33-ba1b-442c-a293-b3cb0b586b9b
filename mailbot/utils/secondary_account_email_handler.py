import logging
from typing import Dict, List, Optional, Tu<PERSON>, Any, Union

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser

from applications.models import EmailTemplate, Application

from mailbot.models import Message, UserMailBotProfile
from mailbot.service.base import BaseMessageService
from mailbot.utils.check import is_primary_profile
from mailbot.utils.defaults import MailBotMessageCategory, MailBotMessageHeaders, MailBotTemplateTag
from mailbot.utils.email_context import get_forward_inbox_secondary_to_primary_context
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.profiles import get_primary_user
from mailbot.service.gmail import GmailService
from mailbot.utils.check import is_associated_mailbot_profile

User = get_user_model()
logger = logging.getLogger(__name__)


class SecondaryAccountEmailHandler:
    """
    Handles email processing for secondary accounts, including forwarding emails
    to the associated primary account when applicable.

    This class is responsible for:
    - Determining if an email should be forwarded to a primary account
    - Retrieving the primary user associated with a secondary account
    - Forwarding regular inbox emails from secondary to primary accounts
    - Forwarding first time sender overlay emails to primary accounts
    - Processing and forwarding email attachments
    """

    def __init__(
        self,
        user: AbstractUser,
        application: Application,
        service: BaseMessageService,
        parsed_message: ParsedMessage,
        sender_domain: str,
        sender_email: str,
        sender_name: str,
    ) -> None:
        """
        Initializes the handler with all required data for email forwarding.

        Args:
            user: The user instance associated with the secondary email account
            application: The current application instance
            service: The email service being used (Gmail, Outlook, etc.)
            parsed_message: The parsed email message object
            sender_domain: The domain of the email sender
            sender_email: The email address of the sender
            sender_name: The display name of the sender
        """
        self.user = user
        self.application = application
        self.service = service
        self.user_email = service.email
        self.parsed_message = parsed_message
        self.sender_domain = sender_domain
        self.sender_email = sender_email
        self.sender_name = sender_name
        self.primary_user = get_primary_user(self.user.user_mailbot_profile)

    def _get_other_recipients(self):
        """
        Extracts a list of email addresses that were also recipients of the message,
        excluding the current user's email.
        Returns:
            str: A string of email addresses the message was also sent to,
                    excluding the user's own email address.
        """
        all_recipients = (
            self.parsed_message.to_name_email + self.parsed_message.cc_name_email + self.parsed_message.bcc_name_email
        )
        # Extract just the email addresses
        email_also_sent_to = [email for _, email in all_recipients]
        email_also_sent_to = [email for email in email_also_sent_to if email != self.service.email]
        return ", ".join(email_also_sent_to)

    def forward_inbox_secondary_to_primary(self) -> Optional[bool]:
        """
        Forwards the email from the secondary account to the associated primary account.

        This method:
        1. Checks if forwarding is needed (based on account type)
        2. Retrieves the primary user
        3. Prepares the email context
        4. Fetches and formats attachments
        5. Sends the forwarded email

        Returns:
            Optional[bool]: None if email is not forwarded or primary user is not found
        """
        if not self.should_forward_inbox_secondary_to_primary():
            logger.info(f"Not forwarding inbox secondary to primary mail for user {self.service.email}.")
            return False

        try:
            is_thread_message = self.parsed_message.message_id != self.parsed_message.thread_id
            message_body = self.parsed_message.html_body or self.parsed_message.text_body
            other_recipients = self._get_other_recipients()
            context = get_forward_inbox_secondary_to_primary_context(
                message_subject=self.parsed_message.subject,
                message_body=message_body,
                sender_email=self.sender_email,
                sender_domain=self.sender_domain,
                secondary_account_email=self.service.email,
                message_text_body=self.parsed_message.text_body,
                other_recipients=other_recipients,
                display_overlay_text=not is_thread_message,
            )
            attachments = self.service.get_attachments(self.parsed_message)

            EmailTemplate.send_email_using_template(
                user=self.user,
                tag=MailBotTemplateTag.FORWARD_INBOX_SECONDARY_TO_PRIMARY.value,
                application=self.application,
                context=context,
                to=self.primary_user.email,
                from_name=f"{self.sender_name}",
                headers={
                    MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.FORWARD_INBOX_SECONDARY_TO_PRIMARY.value,
                    MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value: self.parsed_message.message_id,
                    MailBotMessageHeaders.SECONDARY_ACCOUNT_EMAIL.value: self.service.email,
                    "Reply-To": self.sender_email,
                },
                attachments=attachments,
            )
            logger.info(f"Forwarded inbox secondary to primary mail sent for user {self.service.email}.")
        except Exception as exc:
            logger.exception(
                "Forward inbox secondary to primary mail delivery failed",
                extra={"user": self.service.email, "error_details": exc},
            )

    def should_forward_inbox_secondary_to_primary(self) -> bool:
        """
        Determines whether the email should be forwarded to the primary account.

        This method checks if the current account is a secondary account and if the forwarding policy is set to forward important emails.

        Returns:
            bool: True if the email belongs to a secondary account and should be forwarded,
                 otherwise False.
        """
        if is_primary_profile(self.user.user_mailbot_profile) or not self.primary_user:
            return False
        secondary_profile_preferences = self.user.user_mailbot_profile.secondary_profile_preferences
        if secondary_profile_preferences.get("forwarding_policy") == UserMailBotProfile.FORWARD_IMPORTANT:
            return True
        return False

    def forward_first_time_sender_overlay_mail_to_primary(
        self, context: Dict[str, Any], attachments: List[Tuple[str, bytes, str]]
    ) -> None:
        """
        Forwards the first time sender overlay mail to the primary account.

        This adds the secondary account email to the context and forwards the
        overlay email to the primary account user.

        Args:
            context: The email template context containing all variables needed for
                    the overlay email template
        """
        if not self.should_forward_inbox_secondary_to_primary():
            logger.info(f"Not forwarding first time sender overlay mail to primary for user {self.service.email}.")
            return False

        try:
            # Add secondary account identifier to the context
            context_copy = context.copy()
            other_recipients = self._get_other_recipients()
            context_copy["secondary_account_email"] = self.service.email
            context_copy["other_recipients"] = other_recipients
            EmailTemplate.send_email_using_template(
                user=self.user,
                tag=MailBotTemplateTag.FIRST_TIME_SENDER_OVERLAY.value,
                application=self.application,
                context=context_copy,
                to=self.primary_user.email,
                from_name=f"{self.sender_name}",
                headers={
                    MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.FORWARD_FIRST_TIME_SENDER_OVERLAY_TO_PRIMARY.value,
                    MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value: self.parsed_message.message_id,
                    MailBotMessageHeaders.SECONDARY_ACCOUNT_EMAIL.value: self.service.email,
                    "Reply-To": self.sender_email,
                },
                attachments=attachments,
            )
            logger.info(f"Forwarded first time sender overlay mail to primary mail for user {self.service.email}.")
        except Exception as exc:
            logger.exception(
                "Forward first time sender overlay mail delivery failed",
                extra={"user": self.service.email, "error_details": exc},
            )

    @staticmethod
    def handle_label_change(forwarded_message, original_message, remove_label_name, add_label_name):
        """
        Syncs label changes from a forwarded message in the primary account
        to the corresponding original message in the associated secondary account.

        Typically triggered when a user trains (e.g., whitelist/zapped) on a forwarded message.
        This will archive the overlay message and update the label on the original message  and trigger a webhook from Gmail,
        allowing all downstream logic to run from there.

        Args:
            forwarded_message: Forwarded message object (from the primary account).
            original_message: Original message object (from the secondary account).
            remove_label_name: Label to remove from the original message.
            add_label_name: Label to add to the original message.
        """
        try:
            if not is_associated_mailbot_profile(
                forwarded_message.user_mailbot_profile.id, original_message.user_mailbot_profile.id
            ):
                logger.error(
                    f"User {forwarded_message.user_mailbot_profile.id} is not associated with message {original_message.message_id}"
                )
                return

            gmail_service = GmailService(user_mailbot_profile=original_message.user_mailbot_profile)

            # Move the message between generic labels
            gmail_service.move_between_generic_labels(
                message_id=original_message.message_id,
                remove_label_name=remove_label_name,
                add_label_name=add_label_name,
            )
        except Exception as e:
            logger.exception(f"Failed to update labels for original message {original_message.message_id}: {str(e)}")
        else:
            if overlay_message_id := original_message.metadata.get("overlay_message_id"):
                gmail_service.archive_message(overlay_message_id)
                logger.info(
                    f"Successfully archived overlay message {overlay_message_id} for original message {original_message.message_id}"
                )
            else:
                logger.info(f"No overlay message ID found for original message {original_message.message_id}")
