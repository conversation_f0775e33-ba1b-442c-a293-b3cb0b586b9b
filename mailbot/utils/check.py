import logging

from constance import config as constance_config

from mailbot.models import SecondaryMailBotProfilesThrough, UserMailBotProfile, MailBotUsageLog
from mailbot.utils.defaults import SubscriptionStatistics<PERSON>ey, MailBotProfileMetadataKey
from mailbot.utils.subscription import get_active_subscription
from payments.models import StripeSubscription, StripeCustomer

logger = logging.getLogger(__name__)


def is_primary_profile(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the user's mailbot profile is primary or not.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if primary profile else False
    """
    try:
        SecondaryMailBotProfilesThrough.objects.get(secondary_mailbot_profile=user_mailbot_profile)
    except SecondaryMailBotProfilesThrough.DoesNotExist:
        return True
    else:
        return False


def active_subscription_exists(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the user has an active subscription or not.
    Subscription can be active and still be free, cancel_at_period_end, etc.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if active subscription exists else False
    """
    try:
        secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.get(
            secondary_mailbot_profile=user_mailbot_profile
        )
    except SecondaryMailBotProfilesThrough.DoesNotExist:
        primary_user = user_mailbot_profile.user
    else:
        primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
    try:
        filter_kwargs = {
            "customer_id": primary_user.stripe_customer.id,
            "status__in": [StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
            "cancel_at_period_end": False,
        }
        exists = StripeSubscription.objects.filter(**filter_kwargs).exists()
        if not exists:
            logger.info(f"No active subscription exists for user: {primary_user}")
        return exists
    except StripeCustomer.DoesNotExist:
        return False


def are_tokens_expired(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the user's tokens are expired or not.
    Tokens are expired if the user has not connected their email account with the mailbot.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if tokens are expired else False
    """
    return user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value, False)


def is_mailbot_active(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the mailbot is active or not.
    Mailbot is active if the user has an active subscription, tokens are not expired and mailbot is enabled.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if mailbot is active else False
    """
    mailbot_enabled = user_mailbot_profile.preferences.get("mailbot_enabled", False)
    if not mailbot_enabled:
        return False
    if are_tokens_expired(user_mailbot_profile):
        logger.exception(
            "Tokens are expired for user with mailbot enabled", extra={"user": user_mailbot_profile.user.email}
        )
        return False
    # Currently we deactivate mailbot even if subscription is to be cancelled at period end
    if not active_subscription_exists(user_mailbot_profile):
        logger.exception(
            "No active subscription exists for user with mailbot enabled",
            extra={"user": user_mailbot_profile.user.email},
        )
        return False
    return True


def can_enable_mailbot(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the mailbot can be enabled or not by the user.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if mailbot can be enabled else False
    """
    mailbot_enabled = user_mailbot_profile.preferences.get("mailbot_enabled", False)
    if mailbot_enabled:
        # Mailbot is already enabled
        return False
    if are_tokens_expired(user_mailbot_profile):
        # Tokens are expired
        return False
    if not active_subscription_exists(user_mailbot_profile):
        # No active subscription exists
        return False
    return True


def can_disable_mailbot(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Check if the mailbot can be disabled or not by the user.

    Args:
        user_mailbot_profile : mailbot profile that need to be checked

    Returns:
        bool : True if mailbot can be disabled else False
    """
    mailbot_enabled = user_mailbot_profile.preferences.get("mailbot_enabled", False)
    if not mailbot_enabled:
        # Mailbot is already disabled
        return False
    if are_tokens_expired(user_mailbot_profile):
        # Tokens are expired
        return False
    if not active_subscription_exists(user_mailbot_profile):
        # No active subscription exists
        return False
    return True


def can_delete_more_senders(user_mailbot_profile):
    subscription = get_active_subscription(user_mailbot_profile)
    bulk_delete_limit = subscription.price.metadata.get("bulk_delete_limit")
    if not bulk_delete_limit:
        return True
    try:
        mailbot_usage_log = MailBotUsageLog.objects.get(
            user_mailbot_profile=user_mailbot_profile, subscription_period_start=subscription.current_period_start
        )
    except MailBotUsageLog.DoesNotExist:
        return True
    else:
        senders_trashed = mailbot_usage_log.metadata.get(SubscriptionStatisticsKey.SENDERS_TRASHED.value, 0)
        return senders_trashed < int(bulk_delete_limit)


def can_unsubscribe_more_senders(user_mailbot_profile):
    subscription = get_active_subscription(user_mailbot_profile)
    unsubscribe_limit = subscription.price.metadata.get("unsubscribe_limit")
    if not unsubscribe_limit:
        return True
    try:
        mailbot_usage_log = MailBotUsageLog.objects.get(
            user_mailbot_profile=user_mailbot_profile, subscription_period_start=subscription.current_period_start
        )
    except MailBotUsageLog.DoesNotExist:
        return True
    else:
        senders_unsubscribed = mailbot_usage_log.metadata.get(SubscriptionStatisticsKey.SENDERS_UNSUBSCRIBED.value, 0)
        return senders_unsubscribed < int(unsubscribe_limit)


def show_new_ui_popup(user_mailbot_profile: UserMailBotProfile) -> bool:
    return (
        user_mailbot_profile.user.date_joined.date() <= constance_config.NEW_UI_RELEASE_DATE
        and not user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.NEW_UI_VIEWED.value)
    )


def show_onboarding_tour(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    Determines whether the onboarding tour should be shown to the user.

    The tour is shown if:
    - The user has a primary profile.
    - The user's account was created on or after the onboarding tour release date.
    - The onboarding process has started.
    - The last viewed onboarding step is either not set or is less than 6(last step).

    Args:
        user_mailbot_profile (UserMailBotProfile): The user's mail bot profile.

    Returns:
        bool: True if the onboarding tour should be shown, False otherwise.
    """
    if not is_primary_profile(user_mailbot_profile):
        return False
    onboarding_started = user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ONBOARDING_STARTED_AT.value, False)
    last_onboarding_step_viewed = user_mailbot_profile.metadata.get(
        MailBotProfileMetadataKey.LAST_ONBOARDING_TOUR_STEP_VIEWED.value
    )
    return (
        user_mailbot_profile.user.date_joined.date() >= constance_config.NEW_ONBOARDING_TOUR_RELEASE_DATE
        and onboarding_started
        and (last_onboarding_step_viewed is None or last_onboarding_step_viewed < 6)
    )


def is_associated_mailbot_profile(user_mailbot_profile_id: int, profile_id_to_check: int) -> bool:
    """
    Check if the profile_id_to_check is associated with the user_mailbot_profile_id.
    """
    if user_mailbot_profile_id == profile_id_to_check:
        return True
    associated_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(user_mailbot_profile_id)
    return profile_id_to_check in associated_ids
