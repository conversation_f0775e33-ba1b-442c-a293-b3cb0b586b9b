import logging
import pytz
from django.conf import settings
from django.db.models import Q
from django.utils import timezone

from mailbot.models import Message, SenderProfile, UserMailBotProfile, MailBotGenericLabel
from mailbot.service.factory import MessageServiceFactory
from mailbot.utils.defaults import MailBotMessageHeaders, MailOperationType
from mailbot.utils.email_overlay import get_inbox_overlay_instance
from mailbot.utils.exceptions import MessageIdNotFoundError
from mailbot.utils.jwt import jwt_encode

logger = logging.getLogger(__name__)


def get_digest_context_for_zapped_label(user_mailbot_profile: UserMailBotProfile):
    """
    Get context for digest mail for current user profile.
    Args:
        user_mailbot_profile: User profile for which to generate context.

    Returns:
        Dict[str, Any]: Context used to render digest mail.
    """
    tz_name = user_mailbot_profile.preferences.get("digest_timezone", "UTC")
    current_datetime_utc = timezone.now()
    current_datetime_user_tz = current_datetime_utc.astimezone(pytz.timezone(tz_name))
    past_day_datetime_utc = current_datetime_utc - timezone.timedelta(days=1)
    service = MessageServiceFactory.get_message_service(user_mailbot_profile=user_mailbot_profile)

    # Past day zapped emails for current user
    zapped_items = Message.objects.filter(
        ~Q(metadata__has_key="include_in_digest") | Q(metadata__include_in_digest=True),
        user_mailbot_profile=user_mailbot_profile,
        received_at__gt=past_day_datetime_utc,
        generic_label=MailBotGenericLabel.ZAPPED.value,
    ).only("metadata", "message_id")

    zapped_items_count = 0
    auto_responder_items_count = 0
    zapped_items_context = {}
    auto_responder_items_context = {}
    for zapped_item in zapped_items:
        try:
            message_body_text = service.get_message_body(message_id=zapped_item.message_id, html=False)
        except MessageIdNotFoundError:
            # Skip message that is already deleted by the user from digest
            continue
        else:
            if not message_body_text:
                # There is no message body in an attachment only email
                # TODO: Need to identify other cases where text body is not available due to bad parsing in `html_to_text`
                logger.info(
                    f"Message ID {zapped_item.message_id} for user {user_mailbot_profile.user.email} has no text body for rendering in digest"
                )
                message_body_text = "Attachments"
            sender_email = zapped_item.metadata["from"][1]
            zapped_item_context = {
                "message_id": zapped_item.message_id,
                "subject": zapped_item.subject,
                "body": message_body_text[:100] + ("..." if len(message_body_text) > 100 else ""),
            }
            if zapped_item.metadata.get("auto_responder_verified"):
                auto_responder_items_count += 1
                if sender_email in auto_responder_items_context:
                    auto_responder_items_context[sender_email]["messages"].append(zapped_item_context)
                else:
                    auto_responder_items_context[sender_email] = {
                        "white_list_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "message_id": zapped_item.message_id,
                                "sender_email": sender_email,
                                "new_label": MailBotGenericLabel.WHITE_LIST.value,
                            }
                        ),
                        "zapped_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "sender_email": sender_email,
                                "message_id": zapped_item.message_id,
                                "new_label": MailBotGenericLabel.ZAPPED.value,
                            }
                        ),
                        "messages": [zapped_item_context],
                    }
            else:
                zapped_items_count += 1
                if sender_email in zapped_items_context:
                    zapped_items_context[sender_email]["messages"].append(zapped_item_context)
                else:
                    zapped_items_context[sender_email] = {
                        "white_list_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "sender_email": sender_email,
                                "message_id": zapped_item.message_id,
                                "new_label": MailBotGenericLabel.WHITE_LIST.value,
                            }
                        ),
                        "messages": [zapped_item_context],
                    }

    # Past day user training mails for current user
    user_trainings = (
        SenderProfile.objects.filter(
            user_trained_at__gt=past_day_datetime_utc, user_mailbot_profile=user_mailbot_profile
        )
        .exclude(user_training="")
        .only("sender_email", "sender_name", "user_training")
    )
    return {
        "user_local_date": current_datetime_user_tz.strftime("%B %d, %Y"),
        "name": user_mailbot_profile.user.first_name.capitalize(),
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "training_items": user_trainings,
        "total_count": zapped_items_count + auto_responder_items_count,
        "zapped_items_count": zapped_items_count,
        "auto_responder_items_count": auto_responder_items_count,
        "zapped_items": zapped_items_context,
        "auto_responder_items": auto_responder_items_context,
        "archive_after": user_mailbot_profile.preferences.get("archive_mailbot_mails_after", 24),
    }


def get_digest_context_for_secondary_profile(secondary_user_mailbot_profile, primary_user_mailbot_profile):
    """
    Get context for digest emails from secondary profiles.

    Args:
        secondary_user_mailbot_profile: Secondary profile for which to generate digest
        primary_user_mailbot_profile: Primary profile that will receive the digest

    Returns:
        Dict with context for digest email template
    """
    tz_name = primary_user_mailbot_profile.preferences.get("digest_timezone", "UTC")
    digest_frequency = secondary_user_mailbot_profile.secondary_profile_preferences.get("digest_frequency")
    current_datetime_utc = timezone.now()
    # Convert current time to user's timezone
    current_datetime_user_tz = current_datetime_utc.astimezone(pytz.timezone(tz_name))
    # Calculate start time for digest window in user's timezone
    past_digest_datetime_in_user_tz = current_datetime_user_tz - timezone.timedelta(hours=digest_frequency)

    # Calculate start time for digest window in UTC for database queries
    past_digest_datetime_utc = current_datetime_utc - timezone.timedelta(hours=digest_frequency)

    service = MessageServiceFactory.get_message_service(user_mailbot_profile=secondary_user_mailbot_profile)

    inbox_items = (
        # TODO: Make this include_in_digest consistent it should be include in all email metadata
        Message.objects.filter(
            ~Q(metadata__has_key="include_in_digest") | Q(metadata__include_in_digest=True),
            ~Q(metadata__message_headers__has_key=MailBotMessageHeaders.MESSAGE_CATEGORY.value),
            user_mailbot_profile=secondary_user_mailbot_profile,
            received_at__gt=past_digest_datetime_utc,
            generic_label=MailBotGenericLabel.WHITE_LIST.value,
        )
        .only("metadata", "message_id", "received_at")
        .order_by("-received_at")
    )

    inbox_items_count = 0
    inbox_items_context = {}

    for inbox_item in inbox_items:
        try:
            message_body_text = service.get_message_body(message_id=inbox_item.message_id, html=False)
        except MessageIdNotFoundError:
            # Skip message that is already deleted by the user from digest
            continue
        else:
            # Handle cases where message body might be missing
            if not message_body_text:
                # There is no message body in an attachment only email
                logger.info(
                    f"Message ID {inbox_item.message_id} for user {secondary_user_mailbot_profile.user.email} has no text body for rendering in digest"
                )
                message_body_text = "Attachments"

            sender_email = inbox_item.metadata["from"][1]

            inbox_item_context = {
                "message_id": inbox_item.message_id,
                "subject": inbox_item.subject,
                "received_at": inbox_item.received_at.astimezone(pytz.timezone(tz_name)).strftime("%I:%M %p"),
                "body": message_body_text[:100] + ("..." if len(message_body_text) > 100 else ""),
                "sender_name": inbox_item.metadata["from"][0],
                "view_message_url": f"https://mail.google.com/mail/u/{secondary_user_mailbot_profile.user.email}/#all/{inbox_item.message_id}",
            }

            inbox_items_count += 1

            # Group messages by sender for better organization in digest
            if sender_email in inbox_items_context:
                inbox_items_context[sender_email]["messages"].append(inbox_item_context)
            else:
                inbox_items_context[sender_email] = {
                    "zapped_token": jwt_encode(
                        payload={
                            "exp": timezone.now() + timezone.timedelta(days=1),
                            "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                            "user_mailbot_profile_id": secondary_user_mailbot_profile.id,
                            "sender_email": sender_email,
                            "message_id": inbox_item.message_id,
                            "new_label": MailBotGenericLabel.ZAPPED.value,
                        }
                    ),
                    "messages": [inbox_item_context],
                }

    return {
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "secondary_account_email": secondary_user_mailbot_profile.user.email,
        "primary_account_email": primary_user_mailbot_profile.user.email,
        "total_count": inbox_items_count,
        "inbox_items": inbox_items_context,
        "digest_frequency": digest_frequency,
        "archive_after": primary_user_mailbot_profile.preferences.get("archive_mailbot_mails_after", 24),
        "message_subject": f"Your digest from {secondary_user_mailbot_profile.user.email} for {past_digest_datetime_in_user_tz.strftime('%I:%M %p')} - {current_datetime_user_tz.strftime('%I:%M %p')}",
    }


def get_first_time_sender_overlay_context(
    user_mailbot_profile_id,
    message_id,
    message_subject,
    message_body,
    sender_email,
    sender_domain,
    options_for_overlay,
    message_text_body,
):
    """
    Get context for first time sender overlay for current message.
    Args:
        user_mailbot_profile_id: UserMailBotProfile ID
        message_id: ID of the original message which is moved to grey_list due to FirstTimeSenderFilter
        message_subject: Subject of the original message
        message_body: HTML Body of the original message
        sender_email: First time sender's email address
        sender_domain: First time sender's email domain
        options_for_overlay: options to show in overlay
        message_text_body: Text Body of the original message
    Returns:
        dict: Context used to render first time sender overlay email.
    """
    token_for_user_training = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "operation": MailOperationType.TRAINING_THROUGH_FIRST_TIME_SENDER.value,
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "message_id": message_id,
            "new_label": MailBotGenericLabel.WHITE_LIST.value,
        }
    )
    token_for_domain_training = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "operation": MailOperationType.DOMAIN_TRAINING_THROUGH_FIRST_TIME_SENDER.value,
            "message_id": message_id,
            "new_label": MailBotGenericLabel.WHITE_LIST.value,
        }
    )
    token_for_enable_domain_alerts = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "operation": MailOperationType.PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER.value,
            "message_id": message_id,
            "send_alerts_for_domain": True,
        }
    )
    token_for_disable_domain_alerts = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "operation": MailOperationType.PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER.value,
            "message_id": message_id,
            "send_alerts_for_domain": False,
        }
    )
    return {
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "token_for_user_training": token_for_user_training,
        "token_for_domain_training": token_for_domain_training,
        "token_for_enable_domain_alerts": token_for_enable_domain_alerts,
        "token_for_disable_domain_alerts": token_for_disable_domain_alerts,
        "message_subject": message_subject,
        "message_body": message_body,
        "sender_email": sender_email,
        "sender_domain": sender_domain,
        "options": options_for_overlay,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "preview_text": message_text_body[:150],
    }


def get_lucene_alert_context(
    user_mailbot_profile_id, message_id, message_subject, message_body, sender_email, sender_domain, message_text_body
):
    """
    Get context for lucene alert for current message.
    Args:
        user_mailbot_profile_id: UserMailBotProfile ID
        message_id: ID of the original message
        message_subject: Subject of the original message
        message_body: HTML Body of the original message
        sender_email: First time sender's email address
        sender_domain: First time sender's email domain

    Returns:
        dict: Context used to render lucene alert email.
    """
    skip_archive_token = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "operation": MailOperationType.CANCEL_LUCENE_ALERT.value,
            "message_id": message_id,
            "new_label": MailBotGenericLabel.WHITE_LIST.value,
        }
    )
    skip_archive_sender_token = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "operation": MailOperationType.CANCEL_LUCENE_ALERT_FOR_SENDER.value,
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "message_id": message_id,
            "new_label": MailBotGenericLabel.WHITE_LIST.value,
        }
    )
    skip_archive_domain_token = jwt_encode(
        payload={
            "exp": timezone.now() + timezone.timedelta(days=1),
            "operation": MailOperationType.CANCEL_LUCENE_ALERT_FOR_DOMAIN.value,
            "user_mailbot_profile_id": user_mailbot_profile_id,
            "message_id": message_id,
            "new_label": MailBotGenericLabel.WHITE_LIST.value,
        }
    )
    return {
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "message_subject": message_subject,
        "message_body": message_body,
        "sender_email": sender_email,
        "general_domain": sender_domain in settings.GENERAL_EMAIL_DOMAINS,
        "skip_archive_token": skip_archive_token,
        "skip_archive_sender_token": skip_archive_sender_token,
        "skip_archive_domain_token": skip_archive_domain_token,
        "preview_text": message_text_body[:150],
    }


def get_forward_inbox_secondary_to_primary_context(
    message_subject,
    message_body,
    sender_email,
    sender_domain,
    secondary_account_email,
    message_text_body,
    display_overlay_text,
    other_recipients,
):
    """
    Get context for forward inbox secondary to primary email.
    Args:
        user_mailbot_profile_id: UserMailBotProfile ID
        message_id: ID of the original message which is moved to grey_list due to FirstTimeSenderFilter
        message_subject: Subject of the original message
        message_body: HTML Body of the original message
        sender_email: First time sender's email address
        sender_domain: First time sender's email domain
        display_overlay_text: Whether to display overlay text
        other_recipients: String list of emails that were also sent to
    Returns:
        dict: Context used to render forward inbox secondary to primary email.
    """
    return {
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "message_subject": message_subject,
        "message_body": message_body,
        "sender_email": sender_email,
        "sender_domain": sender_domain,
        "secondary_account_email": secondary_account_email,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "preview_text": message_text_body[:150],
        "display_overlay_text": display_overlay_text,
        "other_recipients": other_recipients,
    }


def get_inbox_overlay_context(message_subject, message_body):
    """
    Get context for inbox overlay for current message.
    """
    inbox_overlay = get_inbox_overlay_instance()
    inbox_overlay_result = inbox_overlay.get_result(subject=message_subject, body=message_body)
    return {
        "label": inbox_overlay_result.label,
        "action": inbox_overlay_result.action,
        "summary": inbox_overlay_result.summary,
    }
