from typing import List
from mailbot.models import MessageCategory, UserMailBotProfile


def should_show_lucene_overlay(message_categories: List[MessageCategory], algo_version) -> bool:
    """Check if any category is Critical Account/Security Alert."""
    if algo_version == UserMailBotProfile.ALGORITHM_V3:
        return any(category.name == "Critical Account/Security Alert" for category in message_categories)
    elif algo_version == UserMailBotProfile.ALGORITHM_V2:
        return False
    else:
        return True
