from typing import Dict, Any, List
import logging
from applications.utils.email import get_normalized_email
from mailbot.models import MailBotGenericLabel, Message, SenderProfile, MessageCategory
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage
from mailbot.models import UserMailBotProfile
from mailbot.models import Message, SenderProfile
from django.db.models import Count, Q
from django.contrib.postgres.aggregates import ArrayAgg
from mailbot.utils.scoring import should_show_lucene_overlay

logger = logging.getLogger(__name__)


class ReadFractionAction(BaseStateAction):
    """
    Assign a label to message based on read fraction for current sender.
    """

    @staticmethod
    def execute(parsed_message: ParsedMessage, filter_result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Executes the default action based on the sender, user profile, and email categories.

        New Algo Logic:
        1. Fetch open rate for given email categories.
        2. Determine if both categories are marked important.
        3. Classify email based on open rate and category importance.
        4. Return the appropriate label.
        """
        message_categories = kwargs.get("message_categories", [])
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        algo_version = kwargs.get("algo_version", UserMailBotProfile.ALGORITHM_V1)
        return ReadFractionAction.base_action(
            parsed_message=parsed_message,
            sender_profile=sender_profile,
            message_categories=message_categories,
            algo_version=algo_version,
        )

    @staticmethod
    def v3_base_action(
        parsed_message: ParsedMessage,
        sender_profile: SenderProfile,
        message_categories: List[MessageCategory],
    ) -> Dict[str, Any]:
        """
        V3 Algorithm Implementation

        Rules:
        - Score ≤1: Zapped
        - Score 2-3: Check read fraction
          - ≥50%: Inbox
          - <50%: Zapped
        - Score 4: Inbox
        """
        score = sum(category.score for category in message_categories)
        action_result = {
            "message_labelled_due_to": MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
            "score": score,
        }

        if score <= 1:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        elif score >= 4:
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        else:  # score is 2 or 3
            read_fraction = ReadFractionAction.get_open_rate(
                parsed_message.user_mailbot_profile_id, sender_profile, message_categories
            )
            if read_fraction >= 0.5:  # 50% threshold
                action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
            else:
                action_result["label_name"] = MailBotGenericLabel.ZAPPED.value

        # Check for lucene overlay
        if should_show_lucene_overlay(message_categories, UserMailBotProfile.ALGORITHM_V3):
            action_result["show_lucene_overlay"] = True

        return action_result

    @staticmethod
    def base_action(
        sender_profile: SenderProfile,
        algo_version,
        parsed_message: ParsedMessage = None,
        message_categories: List[MessageCategory] = None,
    ) -> Dict[str, Any]:
        if algo_version == UserMailBotProfile.ALGORITHM_V3:
            return ReadFractionAction.v3_base_action(parsed_message, sender_profile, message_categories)
        elif algo_version == UserMailBotProfile.ALGORITHM_V2:
            return ReadFractionAction.v2_base_action(parsed_message, sender_profile, message_categories)
        else:
            return ReadFractionAction.old_base_action(sender_profile)

    @staticmethod
    def v2_base_action(parsed_message: ParsedMessage, sender_profile: SenderProfile, message_categories=[]):
        """
        Determines the label based on open rate and category importance.

        - If open_rate < 25%:
          - If both categories are important -> LOW_PRIORITY
          - If one or both are not important -> ZAPPED and include_in_digest=False


        - If 25% <= open_rate < 80%:
          - If both categories are important -> INBOX
          - If one or both are not important -> ZAPPED and include_in_digest=False

        - If open_rate >= 80%:
          - If both categories are important -> INBOX
          - If one or both are not important -> ZAPPED and include_in_digest=True

        Args:
            open_rate (float): The computed open rate for the categories.
            categories_marked_important (bool): Whether both categories are important.

        Returns:
            str: The determined email classification.
        """
        open_rate = ReadFractionAction.get_open_rate(
            parsed_message.user_mailbot_profile_id, sender_profile, message_categories
        )
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.READ_FRACTION_FILTER.value}
        message_has_important_categories = Message.has_important_categories(*(message_categories or []))
        logger.info(
            f"Processing Read Fraction Action | sender_profile_id={sender_profile.id} | Open rate={open_rate} | "
            f"Categories={[category.id for category in message_categories]} | All important={message_has_important_categories}"
        )
        if open_rate < 0.25:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
            if not message_has_important_categories:
                action_result["include_in_digest"] = False
        elif open_rate >= 0.25 and open_rate < 0.8:
            if message_has_important_categories:
                action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
            else:
                action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
                action_result["include_in_digest"] = False
        elif open_rate >= 0.8:
            if message_has_important_categories:
                action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
            else:
                action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
                action_result["include_in_digest"] = True
        return action_result

    @staticmethod
    def get_open_rate(
        user_mailbot_profile_id: UserMailBotProfile, sender_profile: SenderProfile, message_categories
    ) -> float:
        """
        Get the open rate for the given message categories.
        """
        cat_ids = sorted([category.id for category in message_categories])
        counts = (
            Message.objects.filter(
                sender_profile=sender_profile,
                user_mailbot_profile_id=user_mailbot_profile_id,
            )
            .annotate(cat_ids=ArrayAgg("categories", ordering="categories__id"))
            .filter(cat_ids=cat_ids)
            .aggregate(total_count=Count("id"), total_read=Count("id", filter=Q(is_read=True)))
        )
        return counts["total_read"] / counts["total_count"] if counts["total_count"] > 0 else 0

    @staticmethod
    def old_base_action(sender_profile: SenderProfile):
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.SENDER_PROFILE_FILTER.value}
        read_count = sender_profile.read_count
        # Don't include current message count
        total_count = sender_profile.total_count - 1
        if read_count >= 0.9 * total_count:
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        elif read_count >= 0.3 * total_count:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        else:
            action_result["include_in_digest"] = False
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        return action_result
