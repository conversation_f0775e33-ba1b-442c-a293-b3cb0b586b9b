from typing import Any, Dict

from mailbot.models import MailBotGenericLabel, Message, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage


class DefaultAction(BaseStateAction):
    @staticmethod
    def execute(parsed_message: ParsedMessage, filter_result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        user_mailbot_profile = UserMailBotProfile.objects.get(id=parsed_message.user_mailbot_profile_id)
        algo_version = kwargs.get("algo_version", UserMailBotProfile.ALGORITHM_V1)
        if algo_version in (
            UserMailBotProfile.ALGORITHM_V2,
            UserMailBotProfile.ALGORITHM_V3,
        ):
            return DefaultAction.base_action(message_categories=kwargs.get("message_categories", []))
        else:
            return DefaultAction.old_base_action(user_mailbot_profile)

    @staticmethod
    def base_action(message_categories) -> Dict[str, Any]:
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.NO_FILTER.value}
        if Message.has_important_categories(*(message_categories or [])):
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        else:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        return action_result

    @staticmethod
    def old_base_action(user_mailbot_profile: UserMailBotProfile) -> Dict[str, Any]:
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.NO_FILTER.value}
        first_time_sender_treatment = user_mailbot_profile.preferences["first_time_sender_treatment"]
        if first_time_sender_treatment == "inbox":
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        else:
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        return action_result
