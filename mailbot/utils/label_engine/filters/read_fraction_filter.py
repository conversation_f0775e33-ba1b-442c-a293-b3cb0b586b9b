import logging
from typing import Any, Dict
from django.db.models import Count
from applications.utils.email import get_normalized_email
from mailbot.models import Message, SenderProfile, UserMailBotProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage
from constance import config as constance_config
from django.contrib.postgres.aggregates import ArrayAgg

logger = logging.getLogger(__name__)


class ReadFractionFilter(BaseStateFilter):
    """
    A filter that determines whether the read fraction filter should be applied to emails
    based on the sender's email activity within specified categories.

    This filter checks if a sender has sent emails in specific categories above
    a defined threshold (`READ_FRACTION_FILTER_THRESHOLD`).
    """

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )

        message_categories = kwargs.get("message_categories", [])
        algo_version = kwargs.get("algo_version", UserMailBotProfile.ALGORITHM_V1)
        return ReadFractionFilter.base_filter(
            sender_profile=sender_profile,
            parsed_message=parsed_message,
            message_categories=message_categories,
            algo_version=algo_version,
        )

    @staticmethod
    def base_filter(
        sender_profile: SenderProfile, algo_version, parsed_message: ParsedMessage = None, message_categories=[]
    ):
        """
        Checks if a sender has sent enough emails in the given categories to meet the threshold.

         This function checks if the sender has sent at least `READ_FRACTION_FILTER_THRESHOLD` emails
        that belong exclusively to the given categories. The filtering process ensures that only
        messages containing exactly all the specified categories (and no extra ones) are considered.

        Args:
            sender_profile (SenderProfile): The sender profile being evaluated.
            message_categories (List[int], optional): A list of category IDs to check against.

        Returns:
            Dict[str, bool]: A dictionary with a "condition" key indicating whether all
            specified categories meet the threshold.
        """
        # TODO: Use Design Pattern to handle different algo versions
        # If algo version is v2 and message categories are present, use new base filter otherwise use old base filter to check if sender has sent at least N emails
        if (
            algo_version not in (UserMailBotProfile.ALGORITHM_V2, UserMailBotProfile.ALGORITHM_V3)
            or not message_categories
        ):
            logger.info(
                f"Read Fraction Filter - Using old base filter for sender {sender_profile.id} and algo version {algo_version}"
            )
            return ReadFractionFilter.old_base_filter(sender_profile)

        cat_ids = sorted([category.id for category in message_categories])
        message_counts = (
            Message.objects.filter(
                sender_profile=sender_profile, user_mailbot_profile_id=parsed_message.user_mailbot_profile_id
            )
            .annotate(cat_ids=ArrayAgg("categories", ordering="categories__id"))
            .filter(cat_ids=cat_ids)
            .distinct()
            .count()
        )
        meets_threshold = message_counts >= constance_config.READ_FRACTION_FILTER_THRESHOLD
        logger.info(f"Read Fraction Filter - Total Count: {message_counts}, " f"Meets Threshold: {meets_threshold}")
        return {"condition": meets_threshold}

    @staticmethod
    def old_base_filter(sender_profile: SenderProfile):
        if sender_profile.total_count >= 4:
            return {"condition": True}
        else:
            return {"condition": False}
