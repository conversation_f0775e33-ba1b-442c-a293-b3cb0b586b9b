from abc import ABC, abstractmethod
from typing import List, Type, Dict, Any


class BaseStateFilter(ABC):
    """
    Base filter that is based upon state, calls `evaluate` to return dictionary of response.
    """

    @staticmethod
    @abstractmethod
    def evaluate(state, **kwargs) -> Dict[str, Any]:
        """
        Evaluate the filter based on the state passed.
        Args:
            state: state details currently processing
            kwargs(dict): Extra information other than state maybe required for evaluation
        """
        pass

    @staticmethod
    def condition(filter_result: Dict[str, Any]) -> bool:
        return filter_result.get("condition", False)


# Base Filter type can either be Type implementing staticmethod or instance of it implementing instance method
BaseFilterType = Type[BaseStateFilter]


class NegateFilter(BaseStateFilter):
    """
    Filter corresponding to logic NOT of filter passed.
    """

    def __init__(self, filter: BaseFilterType):
        self.filter = filter

    def evaluate(self, state, **kwargs) -> Dict[str, Any]:
        """Evaluates the wrapped filter and return the negated value.

        Args:
            state(BaseState): state details currently processing
            kwargs(dict): Extra information other than state maybe required for evaluation
        """
        result = self.filter.evaluate(state, **kwargs)
        if "condition" in result:
            result["condition"] = not result["condition"]
        else:
            result["condition"] = True
        return result

    def __str__(self):
        return self.filter.__name__


class AnyFilter(BaseStateFilter):
    """
    Filter corresponding to logic OR of filters passed.
    """

    def __init__(self, filters: List[BaseFilterType]):
        self.filters = filters

    def evaluate(self, state, **kwargs) -> Dict[str, Any]:
        """
        Evaluates to True if any filter passes.
        Condition to False if empty list of filters.
        Args:
            state: state details currently processing
            kwargs(dict): Extra information other than state maybe required for evaluation
        """
        result = {"condition": False}
        for filter in self.filters:
            filter_result = filter.evaluate(state, **kwargs)
            if filter.condition(filter_result):
                result.update(filter_result)
        return result

    def __str__(self):
        return ",".join([f.__name__ for f in self.filters])


class AllFilter(BaseStateFilter):
    """
    Filter corresponding to logical AND of filters passed.
    """

    def __init__(self, filters: List[BaseFilterType]):
        self.filters = filters

    def evaluate(self, state, **kwargs) -> Dict[str, Any]:
        """
        Evaluates to True if all filter passes.
        Condition to True if empty list of filters.
        Args:
            state: state details currently processing
            kwargs(dict): Extra information other than state maybe required for evaluation
        """
        result = {"condition": True}
        for filter in self.filters:
            filter_result = filter.evaluate(state, **kwargs)
            if not filter.condition(filter_result):
                return {"condition": False}
            result.update(filter_result)
        return result

    def __str__(self):
        return ",".join([f.__name__ for f in self.filters])
