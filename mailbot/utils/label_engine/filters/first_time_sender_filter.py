import logging
import pytz
from typing import Any, Dict
from datetime import datetime
from execfn.common.utils.datetime import get_utc_datetime
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.utils.email import get_normalized_email
from execfn import ApplicationTag
from mailbot.models import SenderProfile, UserMailBotProfile
from mailbot.utils.email import contains_unsubscribe_link
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage
from constance import config as constance_config
from mailbot.utils.defaults import MailBotProfileMetadataKey

logger = logging.getLogger(__name__)


class FirstTimeSenderFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        if not check_feature(
            user_id=parsed_message.user_id,
            feature_flag=MailBotFeatureFlag.FIRST_TIME_SENDER_OVERLAY,
            application_tag=ApplicationTag.MailBot,
        ):
            return {"condition": False}
        normalized_sender_email = get_normalized_email(parsed_message.from_name_email[1])
        sender_profile = SenderProfile.objects.select_related("user_mailbot_profile").get(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
            normalized_sender_email=normalized_sender_email,
        )
        algo_version = kwargs.get("algo_version", UserMailBotProfile.ALGORITHM_V1)
        return FirstTimeSenderFilter.base_filter(
            sender_profile=sender_profile, algo_version=algo_version, parsed_message=parsed_message
        )

    @staticmethod
    def base_filter(
        sender_profile: SenderProfile,
        algo_version,
        parsed_message: ParsedMessage = None,
    ):
        """
        Determines whether the first-time sender overlay should be displayed.

        Conditions:
        1. Returns True if it's the sender's very first message (total_count == 1).
        2. If not the first message, considers post-onboarding logic:
           - If the sender profile was created *before* or *on* onboarding completion, returns False.
           - If created *after* onboarding:
             - Checks user preferences for 'first_time_sender_treatment'.
             - If treatment is 'low_priority_with_overlay':
               - Checks if the overlay sent count is below both the global threshold
                 and the user-specific limit (if set).
               - Returns True if limits are not reached, False otherwise.
             - If treatment is different, returns False.
        3. Defaults to False if none of the above conditions lead to True.

        Args:
            sender_profile (SenderProfile): The sender's profile containing metadata and email count.
            parsed_message (ParsedMessage): The parsed email message containing the user's mailbot profile ID.

        Returns:
            dict: A dictionary containing `{"condition": True}` if the overlay should be shown,
                otherwise `{"condition": False}`.
        """

        # TODO: Use Design Pattern to handle different algo versions
        if algo_version not in (UserMailBotProfile.ALGORITHM_V2, UserMailBotProfile.ALGORITHM_V3):
            return FirstTimeSenderFilter.old_base_filter(sender_profile, parsed_message)

        if sender_profile.total_count == 1:
            return {"condition": True}
        user_mailbot_profile = sender_profile.user_mailbot_profile
        onboarding_completed_at = FirstTimeSenderFilter.get_onboarding_completed_at(user_mailbot_profile)

        if (not onboarding_completed_at) or (sender_profile.created <= onboarding_completed_at):
            logger.info(
                f"first_time_sender_filter: returning false, onboarding date doesn't exist OR sender profile predates/equals it sender_profile.created: {sender_profile.created} onboarding_completed_at: {onboarding_completed_at}"
            )
            return {"condition": False}

        first_time_sender_treatment = user_mailbot_profile.preferences.get(
            "first_time_sender_treatment", "low_priority_with_overlay"
        )

        if first_time_sender_treatment != "low_priority_with_overlay":
            return {"condition": False}

        fts_overlay_sent_count = sender_profile.metadata.get("fts_overlay_sent_count", 0)
        fts_overlay_global_limit = constance_config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD
        fts_overlay_user_limit = user_mailbot_profile.preferences.get("first_time_sender_overlay_limit", "no-limit")

        try:
            # 1. If user limit is set and is less than or equal to the sent count, do not show the overlay
            if fts_overlay_user_limit != "no-limit" and int(fts_overlay_user_limit) <= fts_overlay_sent_count:
                return {"condition": False}
            # 2. If the sent count is less than the global limit, show the overlay
            if fts_overlay_sent_count < fts_overlay_global_limit:
                return {"condition": True}
        except ValueError as e:
            logger.error(f"Error in first time sender filter: {e}")
            return {"condition": False}

        return {"condition": False}

    @staticmethod
    def old_base_filter(sender_profile: SenderProfile, parsed_message=None):
        """
        Even if the user is a first time sender, do not send the first time sender overlay if either of the following
        is TRUE
            1. email body contains an unsubscribe link
            2. email comes from a generic sender like (help@, updates@, newsletters@, noreply@ and so on)
        """
        if sender_profile.total_count == 1:
            if parsed_message and contains_unsubscribe_link(parsed_message.html_body or parsed_message.text_body):
                logger.info("Email contains unsubscribe link")
                return {"condition": True, "has_unsubscribe_link": True}
            else:
                return {"condition": True}
        else:
            return {"condition": False}

    @staticmethod
    def get_onboarding_completed_at(user_mailbot_profile: UserMailBotProfile):
        onboarding_completed_at_value = user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.ONBOARDING_COMPLETED_AT.value
        )

        parsed_onboarding_date = None
        if onboarding_completed_at_value:
            try:
                if isinstance(onboarding_completed_at_value, datetime):
                    parsed_onboarding_date = onboarding_completed_at_value
                elif isinstance(onboarding_completed_at_value, str):
                    parsed_onboarding_date = get_utc_datetime(
                        input_datetime=onboarding_completed_at_value, time_zone=pytz.timezone("UTC")
                    )
            except (ValueError, TypeError) as e:
                logger.error(
                    f"Error parsing onboarding_completed_at ('{onboarding_completed_at_value}') "
                    f"for UserMailBotProfile ID {user_mailbot_profile.id}: {e}"
                )
        return parsed_onboarding_date
