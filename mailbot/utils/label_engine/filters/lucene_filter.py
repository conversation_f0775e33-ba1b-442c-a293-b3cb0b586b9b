import logging
from typing import Any, Dict

from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.rule_engine.rules.base import BaseRule
from mailbot.models import UserMailBotProfile
from mailbot.utils.scoring import should_show_lucene_overlay

logger = logging.getLogger(__name__)


class LuceneFilter(BaseStateFilter):
    @staticmethod
    def get_category_tags(parsed_message: ParsedMessage):
        """
        Retrieves the category tags for an email based on the evaluation of filtering rules using lucene.

        Args:
           parsed_message: The parsed message

        Returns:
            List[str]: A list of category tags that the email matches.
        """
        base_rule = BaseRule()
        rule_expression = "((PasswordFilter OR CalendarEventFilter) OR ((OtpFilter OR SecurityFilter) OR (BankStatementFilter OR (PaymentsFilter OR OrderTicketFilter))))"
        base_rule.set_rules(rule_expression)
        category_tags = []
        if base_rule.evaluate(parsed_message=parsed_message):
            for filter in base_rule.passed_filters:
                category_tags.append(filter.tag)
        if category_tags:
            logger.info(f"Category tags found are {category_tags}")
        return category_tags

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        algo_version = kwargs.get("algo_version", UserMailBotProfile.ALGORITHM_V1)
        message_categories = kwargs.get("message_categories", [])

        if should_show_lucene_overlay(message_categories, algo_version):
            if category_tags := LuceneFilter.get_category_tags(parsed_message):
                return {"condition": True, "category_tags": category_tags}
        return {"condition": False}
