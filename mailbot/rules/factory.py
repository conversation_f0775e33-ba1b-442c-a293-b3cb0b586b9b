"""
Factory classes for creating rules, filters, and actions from configuration.

This module provides factory classes that can dynamically create rule components
from YAML configuration, enabling flexible rule composition.
"""

import logging
from typing import Dict, Any, Type, List, Optional

from mailbot.utils.label_engine.rules.base import Rule
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.label_engine.actions.base import BaseStateAction

logger = logging.getLogger(__name__)


class FilterFactory:
    """
    Factory for creating filter instances from configuration.

    This factory can create filter instances dynamically based on
    configuration dictionaries, supporting both simple and composite filters.
    """

    def __init__(self):
        """Initialize the filter factory."""
        self._filter_classes: Dict[str, Type[BaseStateFilter]] = {}
        self._register_default_filters()

    def _register_default_filters(self):
        """Register default filter classes."""
        try:
            # Import and register built-in filters
            from mailbot.utils.label_engine.filters import (
                AllFilter,
                AnyFilter,
                NegateFilter,
                DomainTrainingFilter,
                Exception<PERSON>ist<PERSON><PERSON><PERSON>,
                InternalMessageFilter,
                <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON><PERSON><PERSON>,
                <PERSON><PERSON>hread<PERSON>ilt<PERSON>,
                <PERSON>r<PERSON>rainingFilter,
                FirstTimeSenderFilter,
                CalendarInviteFilter,
                SentMessageFilter,
                UserDefinedActionFilter,
            )

            # Register filter classes
            self.register_filter("AllFilter", AllFilter)
            self.register_filter("AnyFilter", AnyFilter)
            self.register_filter("NegateFilter", NegateFilter)
            self.register_filter("DomainTrainingFilter", DomainTrainingFilter)
            self.register_filter("ExceptionListFilter", ExceptionListFilter)
            self.register_filter("InternalMessageFilter", InternalMessageFilter)
            self.register_filter("ReadFractionFilter", ReadFractionFilter)
            self.register_filter("LuceneFilter", LuceneFilter)
            self.register_filter("SameThreadFilter", SameThreadFilter)
            self.register_filter("UserTrainingFilter", UserTrainingFilter)
            self.register_filter("FirstTimeSenderFilter", FirstTimeSenderFilter)
            self.register_filter("CalendarInviteFilter", CalendarInviteFilter)
            self.register_filter("SentMessageFilter", SentMessageFilter)
            self.register_filter("UserDefinedActionFilter", UserDefinedActionFilter)

            # Register enhanced filters
            try:
                from mailbot.rules.filters import (
                    ScoreThresholdFilter,
                    CategoryFilter,
                    ReadFractionFilter,
                    MessagePropertyFilter,
                    CompositeFilter,
                )

                self.register_filter("ScoreThresholdFilter", ScoreThresholdFilter)
                self.register_filter("CategoryFilter", CategoryFilter)
                self.register_filter("ReadFractionFilter", ReadFractionFilter)
                self.register_filter("MessagePropertyFilter", MessagePropertyFilter)
                self.register_filter("CompositeFilter", CompositeFilter)
            except ImportError as e:
                logger.warning(f"Could not import enhanced filters: {e}")

            logger.debug("Registered default filters")

        except ImportError as e:
            logger.warning(f"Could not import some default filters: {e}")

    def register_filter(self, name: str, filter_class: Type[BaseStateFilter]):
        """
        Register a filter class.

        Args:
            name: Filter name/identifier
            filter_class: Filter class to register
        """
        self._filter_classes[name] = filter_class
        logger.debug(f"Registered filter: {name}")

    def create_filter(self, filter_config: Dict[str, Any]) -> BaseStateFilter:
        """
        Create a filter instance from configuration.

        Args:
            filter_config: Filter configuration dictionary

        Returns:
            BaseStateFilter: Created filter instance

        Raises:
            ValueError: If filter type is not registered or config is invalid
        """
        filter_type = filter_config.get("type")
        if not filter_type:
            raise ValueError("Filter configuration must specify 'type'")

        if filter_type not in self._filter_classes:
            raise ValueError(f"Unknown filter type: {filter_type}")

        filter_class = self._filter_classes[filter_type]

        # Handle composite filters (AllFilter, AnyFilter, NegateFilter)
        if filter_type in ["AllFilter", "AnyFilter"]:
            sub_filters = filter_config.get("filters", [])
            if not sub_filters:
                # Empty filter list
                return filter_class([])

            # Create sub-filters recursively
            filter_instances = [self.create_filter(sub_config) for sub_config in sub_filters]
            return filter_class(filter_instances)

        elif filter_type == "NegateFilter":
            sub_filter_config = filter_config.get("filter")
            if not sub_filter_config:
                raise ValueError("NegateFilter requires 'filter' configuration")

            sub_filter = self.create_filter(sub_filter_config)
            return filter_class(sub_filter)

        else:
            # Simple filter - create with parameters
            params = filter_config.get("params", {})
            try:
                return filter_class(**params)
            except TypeError as e:
                # Fallback to parameterless constructor
                logger.warning(f"Failed to create {filter_type} with params {params}: {e}")
                return filter_class()

    def list_filters(self) -> List[str]:
        """
        Get list of registered filter names.

        Returns:
            List[str]: List of filter names
        """
        return list(self._filter_classes.keys())


class ActionFactory:
    """
    Factory for creating action instances from configuration.

    This factory can create action instances dynamically based on
    configuration dictionaries, supporting parameterized actions.
    """

    def __init__(self):
        """Initialize the action factory."""
        self._action_classes: Dict[str, Type[BaseStateAction]] = {}
        self._register_default_actions()

    def _register_default_actions(self):
        """Register default action classes."""
        try:
            # Import and register built-in actions
            from mailbot.utils.label_engine.actions import (
                SetLabelAction,
                ReadFractionAction,
                SameThreadAction,
                FirstTimeSenderAction,
                UserDefinedAction,
                DefaultAction,
                InternalMessageAction,
            )

            # Register action classes
            self.register_action("SetLabelAction", SetLabelAction)
            self.register_action("ReadFractionAction", ReadFractionAction)
            self.register_action("SameThreadAction", SameThreadAction)
            self.register_action("FirstTimeSenderAction", FirstTimeSenderAction)
            self.register_action("UserDefinedAction", UserDefinedAction)
            self.register_action("DefaultAction", DefaultAction)
            self.register_action("InternalMessageAction", InternalMessageAction)

            # Register parameterized actions
            try:
                from mailbot.rules.actions import (
                    ParameterizedSetLabelAction,
                    ParameterizedDefaultAction,
                    AlgorithmRoutingAction,
                    ConditionalAction,
                )

                self.register_action("ParameterizedSetLabelAction", ParameterizedSetLabelAction)
                self.register_action("ParameterizedDefaultAction", ParameterizedDefaultAction)
                self.register_action("AlgorithmRoutingAction", AlgorithmRoutingAction)
                self.register_action("ConditionalAction", ConditionalAction)
            except ImportError as e:
                logger.warning(f"Could not import parameterized actions: {e}")

            logger.debug("Registered default actions")

        except ImportError as e:
            logger.warning(f"Could not import some default actions: {e}")

    def register_action(self, name: str, action_class: Type[BaseStateAction]):
        """
        Register an action class.

        Args:
            name: Action name/identifier
            action_class: Action class to register
        """
        self._action_classes[name] = action_class
        logger.debug(f"Registered action: {name}")

    def create_action(self, action_config: Dict[str, Any]) -> BaseStateAction:
        """
        Create an action instance from configuration.

        Args:
            action_config: Action configuration dictionary

        Returns:
            BaseStateAction: Created action instance

        Raises:
            ValueError: If action type is not registered or config is invalid
        """
        action_type = action_config.get("type")
        if not action_type:
            raise ValueError("Action configuration must specify 'type'")

        if action_type not in self._action_classes:
            raise ValueError(f"Unknown action type: {action_type}")

        action_class = self._action_classes[action_type]

        # Get parameters
        params = action_config.get("params", {})

        try:
            # Try to create with parameters
            return action_class(**params)
        except TypeError:
            # Fallback to parameterless constructor for legacy actions
            logger.warning(f"Creating {action_type} without parameters (legacy mode)")
            return action_class()

    def list_actions(self) -> List[str]:
        """
        Get list of registered action names.

        Returns:
            List[str]: List of action names
        """
        return list(self._action_classes.keys())


class RuleFactory:
    """
    Factory for creating rule instances from configuration.

    This factory orchestrates the creation of complete Rule objects
    from configuration dictionaries.
    """

    def __init__(self):
        """Initialize the rule factory."""
        self.filter_factory = FilterFactory()
        self.action_factory = ActionFactory()

    def create_rule(self, rule_config: Dict[str, Any]) -> Rule:
        """
        Create a rule instance from configuration.

        Args:
            rule_config: Rule configuration dictionary

        Returns:
            Rule: Created rule instance

        Raises:
            ValueError: If rule configuration is invalid
        """
        priority = rule_config.get("priority", 999)

        # Create filter expression
        filter_configs = rule_config.get("filters", [])
        if not filter_configs:
            # Empty filter list - create AllFilter with empty list
            from mailbot.utils.label_engine.filters.base import AllFilter

            filter_expression = AllFilter([])
        elif len(filter_configs) == 1:
            # Single filter
            filter_expression = self.filter_factory.create_filter(filter_configs[0])
        else:
            # Multiple filters - wrap in AllFilter by default
            filters = [self.filter_factory.create_filter(config) for config in filter_configs]
            from mailbot.utils.label_engine.filters.base import AllFilter

            filter_expression = AllFilter(filters)

        # Create actions
        action_configs = rule_config.get("actions", [])
        actions = [self.action_factory.create_action(config) for config in action_configs]

        return Rule(priority=priority, filter_expression=filter_expression, actions=actions)

    def validate_rule_config(self, rule_config: Dict[str, Any]) -> bool:
        """
        Validate rule configuration.

        Args:
            rule_config: Rule configuration to validate

        Returns:
            bool: True if configuration is valid
        """
        try:
            # Check required fields
            if "priority" not in rule_config:
                logger.error("Rule configuration missing 'priority'")
                return False

            if not isinstance(rule_config["priority"], int):
                logger.error("Rule priority must be an integer")
                return False

            # Validate filters
            filter_configs = rule_config.get("filters", [])
            for filter_config in filter_configs:
                if not self._validate_filter_config(filter_config):
                    return False

            # Validate actions
            action_configs = rule_config.get("actions", [])
            if not action_configs:
                logger.error("Rule configuration must specify at least one action")
                return False

            for action_config in action_configs:
                if not self._validate_action_config(action_config):
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating rule configuration: {e}")
            return False

    def _validate_filter_config(self, filter_config: Dict[str, Any]) -> bool:
        """
        Validate filter configuration.

        Args:
            filter_config: Filter configuration to validate

        Returns:
            bool: True if configuration is valid
        """
        if "type" not in filter_config:
            logger.error("Filter configuration missing 'type'")
            return False

        filter_type = filter_config["type"]
        if filter_type not in self.filter_factory.list_filters():
            logger.error(f"Unknown filter type: {filter_type}")
            return False

        # Additional validation for composite filters
        if filter_type in ["AllFilter", "AnyFilter"]:
            sub_filters = filter_config.get("filters", [])
            for sub_filter in sub_filters:
                if not self._validate_filter_config(sub_filter):
                    return False

        elif filter_type == "NegateFilter":
            sub_filter = filter_config.get("filter")
            if not sub_filter or not self._validate_filter_config(sub_filter):
                logger.error("NegateFilter requires valid 'filter' configuration")
                return False

        return True

    def _validate_action_config(self, action_config: Dict[str, Any]) -> bool:
        """
        Validate action configuration.

        Args:
            action_config: Action configuration to validate

        Returns:
            bool: True if configuration is valid
        """
        if "type" not in action_config:
            logger.error("Action configuration missing 'type'")
            return False

        action_type = action_config["type"]
        if action_type not in self.action_factory.list_actions():
            logger.error(f"Unknown action type: {action_type}")
            return False

        return True


# Global factory instances
_rule_factory = None
_filter_factory = None
_action_factory = None


def get_rule_factory() -> RuleFactory:
    """Get the global rule factory instance."""
    global _rule_factory
    if _rule_factory is None:
        _rule_factory = RuleFactory()
    return _rule_factory


def get_filter_factory() -> FilterFactory:
    """Get the global filter factory instance."""
    global _filter_factory
    if _filter_factory is None:
        _filter_factory = FilterFactory()
    return _filter_factory


def get_action_factory() -> ActionFactory:
    """Get the global action factory instance."""
    global _action_factory
    if _action_factory is None:
        _action_factory = ActionFactory()
    return _action_factory
