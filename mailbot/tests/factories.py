import factory
from django.utils import timezone
from factory.django import DjangoModelFactory

from accounts.tests.factories import UserFactory
from mailbot.models import UserMailBotProfile, Message, SenderProfile
from mailbot.utils.defaults import GmailKnownLabelName


class UserMailBotProfileFactory(DjangoModelFactory):
    user = factory.SubFactory(UserFactory)
    label_mappings = factory.Dict({m.name: m.value for m in GmailKnownLabelName})

    class Meta:
        model = UserMailBotProfile


class MessageFactory(DjangoModelFactory):
    user_mailbot_profile = factory.SubFactory(UserMailBotProfileFactory)
    message_id = factory.Faker("uuid4")
    thread_id = factory.Faker("uuid4")
    received_at = factory.LazyFunction(timezone.now)

    class Meta:
        model = Message


class SenderProfileFactory(DjangoModelFactory):
    user_mailbot_profile = factory.SubFactory(UserMailBotProfileFactory)
    read_count = factory.Faker("random_int", min=0, max=100)
    total_count = factory.Faker("random_int", min=0, max=100)
    oldest_timestamp = factory.LazyFunction(timezone.now)
    recent_timestamp = factory.LazyFunction(timezone.now)

    class Meta:
        model = SenderProfile
