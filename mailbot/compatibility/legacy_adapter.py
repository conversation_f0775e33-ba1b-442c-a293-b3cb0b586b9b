"""
Legacy adapter for backward compatibility with existing MailBot interfaces.

This module provides adapters that allow the new algorithm system to work
with existing code without requiring immediate changes to all call sites.
"""
import logging
from typing import Dict, Any, List, Optional
from django.conf import settings

from mailbot.algorithms.engine import get_engine
from mailbot.algorithms.types import MessageContext
from mailbot.utils.message_parser import ParsedMessage
from mailbot.models import UserMailBotProfile, SenderProfile, MessageCategory

logger = logging.getLogger(__name__)


class LegacyAlgorithmAdapter:
    """
    Adapter that provides backward compatibility for algorithm execution.
    
    This class allows existing code to continue using the old interface
    while internally using the new algorithm system.
    """
    
    def __init__(self, use_new_system: bool = None):
        """
        Initialize the adapter.
        
        Args:
            use_new_system: Whether to use new algorithm system. 
                          If None, uses feature flag or setting.
        """
        if use_new_system is None:
            # Check feature flag or setting
            use_new_system = getattr(settings, 'MAILBOT_USE_NEW_ALGORITHM_SYSTEM', False)
        
        self.use_new_system = use_new_system
        self.engine = get_engine() if use_new_system else None
    
    def execute_read_fraction_action(
        self,
        parsed_message: ParsedMessage,
        sender_profile: SenderProfile,
        message_categories: List[MessageCategory],
        algo_version: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute read fraction action with backward compatibility.
        
        Args:
            parsed_message: Parsed email message
            sender_profile: Sender profile
            message_categories: List of message categories
            algo_version: Algorithm version
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Action result in legacy format
        """
        if not self.use_new_system:
            # Use legacy implementation
            return self._execute_legacy_read_fraction_action(
                parsed_message, sender_profile, message_categories, algo_version, **kwargs
            )
        
        try:
            # Use new algorithm system
            profile = UserMailBotProfile.objects.get(id=parsed_message.user_mailbot_profile_id)
            
            # Prepare context
            context = MessageContext(
                message_categories=message_categories,
                sender_profile=sender_profile,
                user_profile=profile
            )
            
            # Process with new engine
            result = self.engine.process_message_legacy(
                parsed_message, profile, message_categories, **kwargs
            )
            
            logger.debug(f"New algorithm system result for {parsed_message.message_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"New algorithm system failed, falling back to legacy: {e}")
            # Fallback to legacy implementation
            return self._execute_legacy_read_fraction_action(
                parsed_message, sender_profile, message_categories, algo_version, **kwargs
            )
    
    def _execute_legacy_read_fraction_action(
        self,
        parsed_message: ParsedMessage,
        sender_profile: SenderProfile,
        message_categories: List[MessageCategory],
        algo_version: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute legacy read fraction action.
        
        This method contains the original algorithm logic for fallback purposes.
        """
        from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
        
        return ReadFractionAction.base_action(
            sender_profile=sender_profile,
            algo_version=algo_version,
            parsed_message=parsed_message,
            message_categories=message_categories
        )


# Global adapter instance
_adapter = None


def get_legacy_adapter() -> LegacyAlgorithmAdapter:
    """
    Get the global legacy adapter instance.
    
    Returns:
        LegacyAlgorithmAdapter: Global adapter instance
    """
    global _adapter
    
    if _adapter is None:
        _adapter = LegacyAlgorithmAdapter()
    
    return _adapter


def execute_algorithm_with_compatibility(
    parsed_message: ParsedMessage,
    sender_profile: SenderProfile,
    message_categories: List[MessageCategory],
    algo_version: str,
    **kwargs
) -> Dict[str, Any]:
    """
    Execute algorithm with backward compatibility.
    
    This function provides a drop-in replacement for the existing
    ReadFractionAction.base_action method.
    
    Args:
        parsed_message: Parsed email message
        sender_profile: Sender profile
        message_categories: List of message categories
        algo_version: Algorithm version
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Action result in legacy format
    """
    adapter = get_legacy_adapter()
    return adapter.execute_read_fraction_action(
        parsed_message, sender_profile, message_categories, algo_version, **kwargs
    )


class FeatureFlag:
    """
    Feature flag management for gradual rollout.
    """
    
    @staticmethod
    def is_new_algorithm_enabled(user_id: Optional[int] = None) -> bool:
        """
        Check if new algorithm system is enabled for a user.
        
        Args:
            user_id: Optional user ID for user-specific flags
            
        Returns:
            bool: True if new system should be used
        """
        # Global feature flag
        if not getattr(settings, 'MAILBOT_USE_NEW_ALGORITHM_SYSTEM', False):
            return False
        
        # User-specific rollout (if implemented)
        if user_id is not None:
            rollout_percentage = getattr(settings, 'MAILBOT_NEW_ALGORITHM_ROLLOUT_PERCENTAGE', 0)
            if rollout_percentage > 0:
                # Simple hash-based rollout
                user_hash = hash(str(user_id)) % 100
                return user_hash < rollout_percentage
        
        return True
    
    @staticmethod
    def should_log_comparison(user_id: Optional[int] = None) -> bool:
        """
        Check if we should log comparison between old and new systems.
        
        Args:
            user_id: Optional user ID
            
        Returns:
            bool: True if comparison should be logged
        """
        return getattr(settings, 'MAILBOT_LOG_ALGORITHM_COMPARISON', False)


class ComparisonLogger:
    """
    Logger for comparing old and new algorithm results.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('mailbot.algorithm_comparison')
    
    def log_comparison(
        self,
        message_id: str,
        old_result: Dict[str, Any],
        new_result: Dict[str, Any],
        user_id: int
    ):
        """
        Log comparison between old and new algorithm results.
        
        Args:
            message_id: Message identifier
            old_result: Result from legacy algorithm
            new_result: Result from new algorithm
            user_id: User identifier
        """
        comparison = {
            'message_id': message_id,
            'user_id': user_id,
            'old_label': old_result.get('label_name'),
            'new_label': new_result.get('label_name'),
            'old_reason': old_result.get('message_labelled_due_to'),
            'new_reason': new_result.get('message_labelled_due_to'),
            'labels_match': old_result.get('label_name') == new_result.get('label_name'),
            'old_metadata': {k: v for k, v in old_result.items() if k not in ['label_name', 'message_labelled_due_to']},
            'new_metadata': {k: v for k, v in new_result.items() if k not in ['label_name', 'message_labelled_due_to']}
        }
        
        self.logger.info(f"Algorithm comparison: {comparison}")


# Global comparison logger
_comparison_logger = None


def get_comparison_logger() -> ComparisonLogger:
    """
    Get the global comparison logger instance.
    
    Returns:
        ComparisonLogger: Global logger instance
    """
    global _comparison_logger
    
    if _comparison_logger is None:
        _comparison_logger = ComparisonLogger()
    
    return _comparison_logger
