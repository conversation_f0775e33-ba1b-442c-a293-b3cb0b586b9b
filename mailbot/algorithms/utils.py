from typing import Optional, Type

from mailbot.models import AlgorithmDefinition, UserMailBotProfile
from mailbot.algorithms.constants import DEFAULT_STRATEGY_VERSION
from mailbot.algorithms import STRATEGY_MAP
from mailbot.algorithms.base import BaseAlgorithmStrategy
import logging

logger = logging.getLogger(__name__)


def get_algorithm_strategy(user_profile: UserMailBotProfile) -> BaseAlgorithmStrategy:
    algo_def_to_use = user_profile.algorithm
    strategy_class: Optional[Type[BaseAlgorithmStrategy]] = None

    if algo_def_to_use and algo_def_to_use.is_active and algo_def_to_use.version_code in STRATEGY_MAP:
        strategy_class = STRATEGY_MAP[algo_def_to_use.version_code]
    else:
        warning_message = f"Algorithm version '{algo_def_to_use.version_code if algo_def_to_use else 'None'}' "
        warning_message += "is inactive." if algo_def_to_use and not algo_def_to_use.is_active else "not found."
        logger.warning(f"Warning: Falling back to default strategy: {DEFAULT_STRATEGY_VERSION} {warning_message}")
        strategy_class = STRATEGY_MAP[DEFAULT_STRATEGY_VERSION]
        algo_def_to_use = get_default_active_algorithm()
    return strategy_class(user_profile=user_profile, algorithm_definition=algo_def_to_use)


def get_default_active_algorithm() -> AlgorithmDefinition:
    try:
        return AlgorithmDefinition.objects.get(version_code=DEFAULT_STRATEGY_VERSION, is_active=True)
    except AlgorithmDefinition.DoesNotExist:
        logger.warning(f"Warning: Default active algorithm '{DEFAULT_STRATEGY_VERSION}' not found.")
        raise ValueError(f"Default active algorithm definition '{DEFAULT_STRATEGY_VERSION}' not found.")
