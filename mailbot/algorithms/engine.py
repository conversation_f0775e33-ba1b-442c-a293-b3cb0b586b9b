"""
Algorithm engine for orchestrating email classification.
"""
import logging
from typing import Optional, Dict, Any

from mailbot.algorithms.base import BaseAlgorithm, ClassificationError
from mailbot.algorithms.registry import get_registry
from mailbot.algorithms.types import LabelDecision, MessageContext
from mailbot.utils.message_parser import ParsedMessage
from mailbot.models import UserMailBotProfile, MessageCategoryThrough

logger = logging.getLogger(__name__)


class AlgorithmEngine:
    """
    Central orchestrator for algorithm execution.
    
    This class provides the main interface for classifying emails using
    the registered algorithm implementations. It handles algorithm selection,
    context preparation, and result processing.
    """
    
    def __init__(self, registry=None):
        """
        Initialize the algorithm engine.
        
        Args:
            registry: Optional algorithm registry. If None, uses global registry.
        """
        self.registry = registry or get_registry()
    
    def process_message(
        self,
        message: ParsedMessage,
        profile: UserMailBotProfile,
        message_categories: Optional[list] = None,
        **kwargs
    ) -> LabelDecision:
        """
        Process a message and return a classification decision.
        
        Args:
            message: Parsed email message
            profile: User's mailbot profile
            message_categories: Optional list of message categories
            **kwargs: Additional context parameters
            
        Returns:
            LabelDecision: Classification decision
            
        Raises:
            ClassificationError: If classification fails
        """
        try:
            # Get algorithm for user's version
            algorithm = self.registry.get_algorithm(profile.algo_version)
            
            # Prepare message context
            context = self._prepare_context(message, message_categories, **kwargs)
            
            # Classify message
            decision = algorithm.classify(message, context, profile)
            
            logger.debug(
                f"Classified message {message.message_id} with algorithm {profile.algo_version}: "
                f"{decision.label_name} ({decision.reason})"
            )
            
            return decision
            
        except Exception as e:
            logger.error(
                f"Failed to classify message {message.message_id} with algorithm {profile.algo_version}: {e}",
                exc_info=True
            )
            raise ClassificationError(f"Classification failed: {e}")
    
    def process_message_legacy(
        self,
        message: ParsedMessage,
        profile: UserMailBotProfile,
        message_categories: Optional[list] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a message and return result in legacy format.
        
        This method provides backward compatibility with the existing codebase
        by returning results in the expected dictionary format.
        
        Args:
            message: Parsed email message
            profile: User's mailbot profile
            message_categories: Optional list of message categories
            **kwargs: Additional context parameters
            
        Returns:
            Dict[str, Any]: Classification result in legacy format
        """
        decision = self.process_message(message, profile, message_categories, **kwargs)
        return decision.to_legacy_format()
    
    def _prepare_context(
        self,
        message: ParsedMessage,
        message_categories: Optional[list] = None,
        **kwargs
    ) -> MessageContext:
        """
        Prepare message context for algorithm processing.
        
        Args:
            message: Parsed email message
            message_categories: Optional list of message categories
            **kwargs: Additional context parameters
            
        Returns:
            MessageContext: Prepared context object
        """
        # Get message categories if not provided
        if message_categories is None:
            message_categories = self._get_message_categories(message.message_id)
        
        # Get sender profile if available
        sender_profile = kwargs.get('sender_profile')
        if sender_profile is None:
            sender_profile = self._get_sender_profile(message)
        
        # Prepare context
        context = MessageContext(
            message_categories=message_categories,
            sender_profile=sender_profile,
            user_profile=kwargs.get('user_profile'),
            thread_context=kwargs.get('thread_context'),
            historical_data=kwargs.get('historical_data')
        )
        
        return context
    
    def _get_message_categories(self, message_id: str) -> list:
        """
        Get message categories from database.
        
        Args:
            message_id: Message identifier
            
        Returns:
            list: List of message categories
        """
        try:
            through_instances = MessageCategoryThrough.objects.filter(
                message__message_id=message_id
            ).select_related("category")
            return [instance.category for instance in through_instances]
        except Exception as e:
            logger.warning(f"Failed to get message categories for {message_id}: {e}")
            return []
    
    def _get_sender_profile(self, message: ParsedMessage):
        """
        Get sender profile for the message.
        
        Args:
            message: Parsed email message
            
        Returns:
            SenderProfile or None: Sender profile if available
        """
        try:
            from applications.utils.email import get_normalized_email
            from mailbot.models import SenderProfile
            
            sender_email = message.from_name_email[1]
            normalized_sender_email = get_normalized_email(sender_email)
            
            return SenderProfile.objects.get(
                normalized_sender_email=normalized_sender_email,
                user_mailbot_profile_id=message.user_mailbot_profile_id,
            )
        except Exception as e:
            logger.warning(f"Failed to get sender profile for {message.message_id}: {e}")
            return None
    
    def get_available_algorithms(self) -> list:
        """
        Get list of available algorithm versions.
        
        Returns:
            list: List of available algorithm versions
        """
        return self.registry.list_versions()
    
    def validate_algorithm_config(self, version: str, config_dict: Dict[str, Any]) -> bool:
        """
        Validate configuration for an algorithm version.
        
        Args:
            version: Algorithm version identifier
            config_dict: Configuration dictionary to validate
            
        Returns:
            bool: True if configuration is valid
        """
        try:
            from mailbot.algorithms.types import AlgorithmConfig
            
            algorithm = self.registry.get_algorithm(version)
            config = AlgorithmConfig(**config_dict)
            return algorithm.validate_config(config)
        except Exception as e:
            logger.error(f"Failed to validate config for {version}: {e}")
            return False


# Global engine instance
_engine = None


def get_engine() -> AlgorithmEngine:
    """
    Get the global algorithm engine instance.
    
    Returns:
        AlgorithmEngine: Global engine instance
    """
    global _engine
    
    if _engine is None:
        _engine = AlgorithmEngine()
    
    return _engine


def process_message_with_algorithm(
    message: ParsedMessage,
    profile: UserMailBotProfile,
    message_categories: Optional[list] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Process a message using the algorithm engine (legacy interface).
    
    Args:
        message: Parsed email message
        profile: User's mailbot profile
        message_categories: Optional list of message categories
        **kwargs: Additional context parameters
        
    Returns:
        Dict[str, Any]: Classification result in legacy format
    """
    engine = get_engine()
    return engine.process_message_legacy(message, profile, message_categories, **kwargs)
