"""
Algorithm V3 implementation for MailBot.

This algorithm uses score-based classification with the following logic:
- Score ≤1: ZAPPED
- Score 2-3: Check read fraction (≥50% → INBOX, <50% → ZAPPED)
- Score ≥4: INBOX
- Special handling for "Critical Account/Security Alert" categories
"""
import logging
from typing import Dict, Any

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.algorithms.types import LabelDecision, MessageContext, AlgorithmConfig
from mailbot.models import MailBotGenericLabel, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.message_parser import ParsedMessage
from mailbot.scoring.base import get_scoring_engine
from mailbot.utils.scoring import should_show_lucene_overlay

logger = logging.getLogger(__name__)


class AlgorithmV3(BaseAlgorithm):
    """
    Version 3 algorithm implementation.
    
    This algorithm uses category scores to make classification decisions
    with read fraction as a secondary factor for medium scores.
    """
    
    def get_version(self) -> str:
        """Get algorithm version identifier."""
        return "v3"
    
    def classify(
        self, 
        message: ParsedMessage, 
        context: MessageContext,
        profile: UserMailBotProfile
    ) -> LabelDecision:
        """
        Classify message using V3 algorithm logic.
        
        Args:
            message: Parsed email message
            context: Message context including categories
            profile: User's mailbot profile
            
        Returns:
            LabelDecision: Classification decision
        """
        try:
            # Calculate category score
            scoring_engine = get_scoring_engine()
            scorer_name = self.get_scoring_rule("method", "category_sum")
            score = scoring_engine.calculate_score(scorer_name, context)
            score_breakdown = scoring_engine.get_score_breakdown(scorer_name, context)
            
            # Apply classification logic
            decision = self._apply_classification_logic(score, context, message, profile)
            
            # Add score information to decision
            decision.score_breakdown = score_breakdown
            decision.metadata["score"] = score
            
            # Check for lucene overlay
            if should_show_lucene_overlay(context.message_categories, UserMailBotProfile.ALGORITHM_V3):
                decision.show_lucene_overlay = True
                decision.metadata["show_lucene_overlay"] = True
            
            logger.debug(
                f"V3 classification for message {message.message_id}: "
                f"score={score}, label={decision.label_name}, reason={decision.reason}"
            )
            
            return decision
            
        except Exception as e:
            logger.error(f"V3 classification failed for message {message.message_id}: {e}")
            # Return default decision on error
            return LabelDecision(
                label_name=MailBotGenericLabel.ZAPPED.value,
                reason=MailBotMessageLabelReason.DEFAULT_ACTION.value,
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _apply_classification_logic(
        self, 
        score: float, 
        context: MessageContext, 
        message: ParsedMessage,
        profile: UserMailBotProfile
    ) -> LabelDecision:
        """
        Apply V3 classification logic based on score.
        
        Args:
            score: Calculated category score
            context: Message context
            message: Parsed message
            profile: User profile
            
        Returns:
            LabelDecision: Classification decision
        """
        low_threshold = self.get_threshold("low_score_threshold", 1.0)
        high_threshold = self.get_threshold("high_score_threshold", 4.0)
        
        if score <= low_threshold:
            return LabelDecision(
                label_name=MailBotGenericLabel.ZAPPED.value,
                reason=MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
                confidence=0.9,
                metadata={"classification_reason": "low_score"}
            )
        
        elif score >= high_threshold:
            return LabelDecision(
                label_name=MailBotGenericLabel.WHITE_LIST.value,
                reason=MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
                confidence=0.9,
                metadata={"classification_reason": "high_score"}
            )
        
        else:
            # Medium score (2-3): Check read fraction
            return self._check_read_fraction(context, message, profile)
    
    def _check_read_fraction(
        self, 
        context: MessageContext, 
        message: ParsedMessage,
        profile: UserMailBotProfile
    ) -> LabelDecision:
        """
        Check read fraction for medium scores.
        
        Args:
            context: Message context
            message: Parsed message
            profile: User profile
            
        Returns:
            LabelDecision: Classification decision based on read fraction
        """
        try:
            # Get read fraction from existing logic
            from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
            
            read_fraction = ReadFractionAction.get_open_rate(
                message.user_mailbot_profile_id, 
                context.sender_profile, 
                context.message_categories
            )
            
            read_threshold = self.get_threshold("read_fraction_threshold", 0.5)
            
            if read_fraction >= read_threshold:
                return LabelDecision(
                    label_name=MailBotGenericLabel.WHITE_LIST.value,
                    reason=MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
                    confidence=0.7,
                    metadata={
                        "classification_reason": "good_engagement",
                        "read_fraction": read_fraction
                    }
                )
            else:
                return LabelDecision(
                    label_name=MailBotGenericLabel.ZAPPED.value,
                    reason=MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
                    confidence=0.7,
                    metadata={
                        "classification_reason": "poor_engagement",
                        "read_fraction": read_fraction
                    }
                )
                
        except Exception as e:
            logger.warning(f"Failed to get read fraction: {e}")
            # Default to zapped if we can't get read fraction
            return LabelDecision(
                label_name=MailBotGenericLabel.ZAPPED.value,
                reason=MailBotMessageLabelReason.READ_FRACTION_FILTER.value,
                confidence=0.5,
                metadata={
                    "classification_reason": "read_fraction_error",
                    "error": str(e)
                }
            )
    
    def get_configuration_schema(self) -> Dict[str, Any]:
        """
        Get configuration schema for V3 algorithm.
        
        Returns:
            Dict[str, Any]: JSON schema for configuration validation
        """
        return {
            "type": "object",
            "properties": {
                "thresholds": {
                    "type": "object",
                    "properties": {
                        "low_score_threshold": {
                            "type": "number",
                            "minimum": 0.0,
                            "maximum": 10.0,
                            "default": 1.0
                        },
                        "high_score_threshold": {
                            "type": "number",
                            "minimum": 0.0,
                            "maximum": 10.0,
                            "default": 4.0
                        },
                        "read_fraction_threshold": {
                            "type": "number",
                            "minimum": 0.0,
                            "maximum": 1.0,
                            "default": 0.5
                        }
                    }
                },
                "scoring_rules": {
                    "type": "object",
                    "properties": {
                        "method": {
                            "type": "string",
                            "enum": ["category_sum", "weighted_category"],
                            "default": "category_sum"
                        },
                        "max_score": {
                            "type": "number",
                            "minimum": 1.0,
                            "default": 4.0
                        }
                    }
                },
                "feature_flags": {
                    "type": "object",
                    "properties": {
                        "lucene_overlay_enabled": {
                            "type": "boolean",
                            "default": True
                        },
                        "critical_alert_detection": {
                            "type": "boolean",
                            "default": True
                        }
                    }
                }
            },
            "required": ["thresholds", "scoring_rules"]
        }
