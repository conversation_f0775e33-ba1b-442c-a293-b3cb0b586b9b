from rest_framework import serializers
from mailbot.models import (
    SenderP<PERSON><PERSON><PERSON>,
    UserMailBotProfile,
    SecondaryMailBotProfilesThrough,
    UserMailBotAnalytics,
    SenderUnsubscribeDetail,
)
from mailbot.utils.defaults import MailBotProfileMetadataKey, ActionTypes
from mailbot.utils.identify_sender import is_sender_blocked_by_emailzap
from payments.models import StripePrice, CouponPriceThrough, StripeCoupon, StripeInvoice
from payments.api.serializers import StripeCouponSerializer
from mailbot.utils.digest import calculate_next_digest_hour


class MicrosoftCallbackSerializer(serializers.Serializer):
    token_type = serializers.CharField()
    # scopes are present as space separated single string
    scope = serializers.CharField()
    expires_in = serializers.IntegerField()
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()
    id_token_claims = serializers.JSONField(default=dict)


class MessageWebhookSerializer(serializers.Serializer):
    TYPE_CREATED = "created"
    TYPE_UPDATED = "updated"
    TYPE_DELETED = "deleted"
    CHANGE_TYPE_CHOICES = (
        (TYPE_CREATED, "Created"),
        (TYPE_UPDATED, "Updated"),
        (TYPE_DELETED, "Deleted"),
    )
    subscriptionId = serializers.CharField(source="subscription_id")
    subscriptionExpirationDateTime = serializers.DateTimeField(source="subscription_expiration_datetime")
    changeType = serializers.ChoiceField(choices=CHANGE_TYPE_CHOICES, source="change_type")
    clientState = serializers.CharField(source="client_state")
    resourceData = serializers.JSONField(source="resource_data")


class SenderProfileSerializer(serializers.ModelSerializer):
    read_fraction = serializers.FloatField(read_only=True)
    inbox_count = serializers.IntegerField(read_only=True)
    blocked_by_emailzap = serializers.SerializerMethodField()

    class Meta:
        model = SenderProfile
        fields = (
            "id",
            "sender_email",
            "sender_name",
            "sender_domain",
            "total_count",
            "read_fraction",
            "user_training",
            "blocked_by_emailzap",
            "recent_timestamp",
            "user_action",
            "user_action_reason",
            "inbox_count",
            "scanned_count",
            "user_mailbot_profile_id",
        )

    def get_blocked_by_emailzap(self, obj: SenderProfile):
        user_mailbot_profile = obj.user_mailbot_profile
        if not user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value):
            return False
        user = self.context["request"].user
        if obj.user_training:
            return False
        return is_sender_blocked_by_emailzap(sender_profile=obj, user_email=user.email)


class MailbotProfileSerializer(serializers.ModelSerializer):
    primary = serializers.SerializerMethodField()

    class Meta:
        model = UserMailBotProfile
        fields = ("id", "preferences", "metadata", "primary", "user_picture", "service_provider")
        read_only_fields = ("id", "metadata", "primary", "user_picture", "service_provider")

    def get_primary(self, instance) -> bool:
        """
        Return if the current profile is a primary mailbot profile or secondary
        """
        return not SecondaryMailBotProfilesThrough.objects.filter(secondary_mailbot_profile_id=instance.id).exists()


class SecondaryMailbotProfilePreferencesSerializer(serializers.Serializer):
    forwarding_policy = serializers.ChoiceField(choices=UserMailBotProfile.FORWARDING_CHOICES, required=False)
    digest_frequency = serializers.IntegerField(required=False)
    digest_scheduled_hour = serializers.IntegerField(required=False, read_only=True)

    def validate(self, data):
        data = super().validate(data)
        if "digest_frequency" in data:
            data["digest_scheduled_hour"] = calculate_next_digest_hour(data["digest_frequency"])
        return data


class SecondaryMailBotProfilesSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    user_picture = serializers.CharField(read_only=True)
    primary = serializers.BooleanField(read_only=True)
    preferences = serializers.JSONField(read_only=True)
    secondary_profile_preferences = serializers.JSONField(read_only=True)

    class Meta:
        model = UserMailBotProfile
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
            "primary",
            "user_picture",
            "preferences",
            "secondary_profile_preferences",
        )


class MailBotPriceCatalogueSerializer(serializers.ModelSerializer):
    is_current_plan = serializers.BooleanField(read_only=True)
    coupon = serializers.SerializerMethodField()
    final_amount = serializers.SerializerMethodField()

    class Meta:
        model = StripePrice
        fields = (
            "id",
            "product",
            "nickname",
            "currency",
            "unit_amount",
            "interval",
            "interval_count",
            "metadata",
            "is_current_plan",
            "coupon",
            "final_amount",
        )

    def _get_coupon(self, obj):
        version = self.context["pricing_version"]
        if version:
            try:
                coupon = (
                    CouponPriceThrough.objects.select_related("coupon")
                    .get(
                        price=obj,
                        version=version,
                    )
                    .coupon
                )
            except CouponPriceThrough.DoesNotExist:
                return None
            else:
                if not coupon:
                    return None
                if coupon.duration in (StripeCoupon.DURATION_ONCE, StripeCoupon.DURATION_REPEATING):
                    # return coupon only if it is not used until now
                    if (
                        self.context["stripe_customer"]
                        and StripeInvoice.objects.filter(
                            customer=self.context["stripe_customer"],
                            status=StripeInvoice.STATUS_PAID,
                            subscription__price=obj,
                        )
                        .exclude(stripe_data__discount=None)
                        .exists()
                    ):
                        return None
                return coupon
        return None

    def get_final_amount(self, obj):
        coupon = self._get_coupon(obj)
        if not coupon:
            return obj.unit_amount
        else:
            if coupon.amount_off:
                return obj.unit_amount - coupon.amount_off
            elif coupon.percent_off:
                return round(obj.unit_amount - (coupon.percent_off * obj.unit_amount) / 100)
            else:
                return obj.unit_amount

    def get_coupon(self, obj):
        coupon = self._get_coupon(obj)
        if coupon:
            return StripeCouponSerializer(coupon).data
        else:
            return None


class UnifiedInboxUserAnalyticsSerializer(serializers.ModelSerializer):
    trashed_count = serializers.IntegerField(read_only=True, source="statistics.trashed_count")
    senders_unsubscribed = serializers.IntegerField(read_only=True, source="statistics.senders_unsubscribed")
    messages_scanned = serializers.IntegerField(read_only=True, source="statistics.messages_scanned")
    messages_zapped = serializers.IntegerField(read_only=True, source="statistics.messages_zapped")

    class Meta:
        model = UserMailBotAnalytics
        fields = [
            "user_mailbot_profile_id",
            "trashed_count",
            "senders_unsubscribed",
            "messages_scanned",
            "messages_zapped",
        ]


class ActionRequestSerializer(serializers.Serializer):
    action_type = serializers.ChoiceField(
        choices=[(action_type.value, action_type.name) for action_type in ActionTypes], required=True, allow_blank=False
    )
    details = serializers.JSONField(required=True)


class UnifiedInboxManageSendersSerializer(serializers.ModelSerializer):
    class Meta:
        model = SenderProfile
        fields = (
            "id",
            "sender_email",
            "sender_name",
            "sender_domain",
            "total_count",
            "recent_timestamp",
            "user_action",
            "user_mailbot_profile_id",
        )


class UnifiedInboxDeleteSerializer(serializers.ModelSerializer):
    deleted = serializers.SerializerMethodField()

    class Meta:
        model = SenderProfile
        fields = (
            "id",
            "sender_email",
            "sender_name",
            "sender_domain",
            "total_count",
            "recent_timestamp",
            "user_action",
            "deleted",
            "user_mailbot_profile_id",
        )

    def get_deleted(self, obj: SenderProfile) -> bool:
        if isinstance(obj.metadata, dict):
            return obj.metadata.get("deleted") is True
        return False


class UnifiedInboxUnsubscribeSerializer(serializers.ModelSerializer):
    unsubscribed = serializers.SerializerMethodField()

    class Meta:
        model = SenderProfile
        fields = (
            "id",
            "sender_email",
            "sender_name",
            "sender_domain",
            "total_count",
            "recent_timestamp",
            "user_action",
            "unsubscribed",
            "user_mailbot_profile_id",
        )

    def get_unsubscribed(self, obj: SenderProfile) -> bool:
        return SenderUnsubscribeDetail.objects.filter(sender_profile=obj, unsubscribed=True).exists()
