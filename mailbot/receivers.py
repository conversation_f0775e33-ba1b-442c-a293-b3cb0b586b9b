import datetime
import logging

import pytz
import stripe
from constance import config as constance_config
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.db.models.signals import post_save, post_delete, pre_delete
from django.dispatch import receiver
from django.utils import timezone
from django_ses.signals import click_received
from stripe import InvalidRequestError

from accounts.signals import refresh_token_expired
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.feature_flags.registry import reset_launch_darkly_instance_map
from applications.feature_flags.signals import feature_flag_target_users_changed
from applications.models import ScheduledTask
from execfn import ApplicationTag
from execfn.settings import APP_ENV_PROD
from jarvis.signals import execute_jarvis_workflow_signal
from jarvis.tasks import queue_jarvis_workflow_for_execution
from mailbot.feature_flags import FeatureFlagMailBotTargetChanged
from mailbot.models import MailBotGeneric<PERSON>abel, SecondaryMailBotProfiles<PERSON>hrough, UserMailBotProfile, Message
from mailbot.signals import (
    post_app_profile_delete,
    scan_completed,
    post_email_read,
    on_mailbot_activate,
    post_label_change,
)
from mailbot.tasks import (
    on_refresh_token_expired,
    initialise_onboarding_tasks,
    batch_analyze_email_message,
)
from mailbot.utils.base import activate_mailbot, deactivate_mailbot
from mailbot.utils.defaults import (
    GmailKnownLabelName,
    MailBotMessageHeaders,
    MailBotScheduledTasks,
    MailBotMessageCategory,
    AUTO_RESPONDER_MAIL_VERIFICATION_URL,
    OutlookKnownLabelName,
    AnalyticsStatisticsKey,
    MailBotProfileMetadataKey,
    OVERLAY_MESSAGE_CATEGORIES,
    FORWARDED_OVERLAY_MESSAGE_CATEGORIES,
)
from mailbot.utils.email_scheduler import schedule_trial_emails
from mailbot.utils.message_parser import index_headers
from mailbot.utils.statistics import update_statistics_in_analytics
from mailbot.utils.secondary_account_email_handler import SecondaryAccountEmailHandler
from payments.models import StripeInvoice, StripeSubscription, StripePrice
from payments.signals import subscription_canceled, subscription_renewed, invoice_paid
from mailbot.utils.base import analyze_messages_in_chunks

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(signal=post_save, sender=Message)
def schedule_archival(instance: Message, created: bool, **kwargs):
    """
    Schedule task for auto archival if mail is sent by mailbot and configured in AUTO_ARCHIVAL_REGISTRY.
    """
    if (
        created
        and instance.internal_message_category
        and check_feature(
            user_id=instance.user_mailbot_profile.user_id,
            feature_flag=MailBotFeatureFlag.AUTO_ARCHIVAL,
            application_tag=ApplicationTag.MailBot,
        )
        and instance.user_mailbot_profile.preferences.get("archive_mailbot_mails_enabled", True)
    ):
        if archive_in_minutes := constance_config.AUTO_ARCHIVE_DEFAULTS.get(instance.internal_message_category):
            if archive_mailbot_mails_after := instance.user_mailbot_profile.preferences.get(
                "archive_mailbot_mails_after"
            ):
                archive_in_minutes = int(archive_mailbot_mails_after) * 60
            ScheduledTask.create_one_off_task(
                user=instance.user_mailbot_profile.user,
                start_time=timezone.now() + timezone.timedelta(minutes=archive_in_minutes),
                task=MailBotScheduledTasks.AUTO_ARCHIVER.value,
                task_args=(instance.message_id,),
                metadata={"message_id": instance.message_id},
            )
            logger.info(
                f"Scheduled auto archival for message_id {instance.message_id}, user "
                f"{instance.user_mailbot_profile.user.email}, category {instance.internal_message_category}, in "
                f"{archive_in_minutes} minutes"
            )


@receiver(signal=post_save, sender=Message)
def increment_processed_count(instance: Message, created: bool, **kwargs):
    """
    Increment total processed count for user in cache on message creation
    """
    if created:
        update_statistics_in_analytics(
            user_mailbot_profile=instance.user_mailbot_profile,
            key=AnalyticsStatisticsKey.MESSAGES_SCANNED.value,
            value=1,
        )


@receiver(signal=post_save, sender=Message)
def save_overlay_message_id(instance: Message, created: bool, **kwargs):
    """
    Save overlay message id in the original message's metadata for faster query in mail operations.
    """
    if created and instance.internal_message_category in [
        *OVERLAY_MESSAGE_CATEGORIES,
        *FORWARDED_OVERLAY_MESSAGE_CATEGORIES,
    ]:
        original_message_id = instance.metadata[MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.name]
        try:
            original_message = Message.objects.get(message_id=original_message_id)
        except Message.DoesNotExist:
            logger.error(
                "Original message ID does not exist for overlay", extra={"overlay_message_id": instance.message_id}
            )
        else:
            if instance.internal_message_category in OVERLAY_MESSAGE_CATEGORIES:
                original_message.metadata["overlay_message_id"] = instance.message_id
            elif instance.internal_message_category in FORWARDED_OVERLAY_MESSAGE_CATEGORIES:
                original_message.metadata["forwarded_overlay_message_id"] = instance.message_id
            original_message.save(update_fields=("metadata",))


@receiver(invoice_paid, sender=StripeInvoice)
def post_invoice_paid(instance: StripeInvoice, **kwargs):
    """
    Receiver to handle post subscription cancellation steps for mailbot
    """
    secondary_mailbot_profile_user_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
        user_mailbot_profile_id=instance.customer.user.user_mailbot_profile.id
    )
    if secondary_mailbot_profile_user_ids:
        linked_profile_count = len(secondary_mailbot_profile_user_ids) + 1
        if linked_profile_count > int(instance.subscription.product.metadata.get("linked_accounts_max_count", 2)):
            raise ValueError("Max linked profile limit exceeded")
    user_mailbot_profile = instance.customer.user.user_mailbot_profile
    if user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value):
        return
    # Don't subscribe watch channel before onboarding as it is handled in the last step of onboarding
    # Stripe marks subscription as incomplete before marking it as active, and paid_invoice webhook may be received
    # by us earlier.
    activate_mailbot(user_mailbot_profile, run_watch_channels=False, check_active_subscription_exists=False)
    if settings.APP_ENV == APP_ENV_PROD:
        initialise_onboarding_tasks.apply_async(
            kwargs={"user_mailbot_profile_id": user_mailbot_profile.id}, queue="celery_long_running"
        )
    else:
        initialise_onboarding_tasks.delay(user_mailbot_profile_id=user_mailbot_profile.id)


@receiver(subscription_canceled)
@receiver(post_delete, sender=StripeSubscription)
def post_subscription_canceled(instance: StripeSubscription, **kwargs):
    try:
        primary_profile = instance.customer.user.user_mailbot_profile
    except UserMailBotProfile.DoesNotExist:
        logger.info(f"User mailbot profile not found for subscription {instance.id}")
    else:
        if not primary_profile.preferences["mailbot_enabled"]:
            logger.info(
                f"Mailbot already disabled for user {instance.customer.user.email} when subscription was canceled"
            )
            return
        if StripeSubscription.objects.filter(
            customer=instance.customer,
            status__in=[StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
            cancel_at_period_end=False,
        ).exists():
            # Ideally this should not happen, but if it does, we should not deactivate mailbot profiles
            logger.exception("Not deactivating mailbot profiles for user as another active subscription exists")
            return
        logger.info(f"Subscription canceled for user {instance.customer.user.email}, deactivating mailbot profiles")
        deactivate_mailbot(primary_profile)
        secondary_mailbot_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id=primary_profile.id
        )
        secondary_mailbot_profiles = UserMailBotProfile.objects.select_related("user").filter(
            id__in=secondary_mailbot_profile_ids
        )
        for secondary_mailbot_profile in secondary_mailbot_profiles:
            deactivate_mailbot(secondary_mailbot_profile)
            logger.info(f"Successfully deactivated mailbot profiles for user {secondary_mailbot_profile.user.email}")


@receiver(post_save, sender=StripeSubscription)
def post_subscription_created(instance: StripeSubscription, created: bool, **kwargs):
    if created:
        user_mailbot_profile = UserMailBotProfile.objects.get(user=instance.customer.user)
        if instance.price.nickname == "freebie":
            if not instance.cancel_at:
                logger.info("Free subscription created, canceling after 7 days")
                stripe.Subscription.modify(
                    instance.id, cancel_at=int((instance.current_period_start + timezone.timedelta(days=7)).timestamp())
                )
            # Schedule trial expiry emails
            schedule_trial_emails(user_mailbot_profile=user_mailbot_profile)
        # If user's mailbot is disabled but onboarding is completed, it means user has retaken the subscription,
        # activate the mailbot.
        if not user_mailbot_profile.preferences["mailbot_enabled"] and user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value
        ):
            activate_mailbot(user_mailbot_profile=user_mailbot_profile)


@receiver(post_save, sender=SecondaryMailBotProfilesThrough)
def post_secondary_profile_created(instance: SecondaryMailBotProfilesThrough, created: bool, **kwargs):
    if created:
        profile = instance.secondary_mailbot_profile
        activate_mailbot(profile, run_watch_channels=False)
        if settings.APP_ENV == APP_ENV_PROD:
            initialise_onboarding_tasks.apply_async(
                kwargs={"user_mailbot_profile_id": profile.id}, queue="celery_long_running"
            )
        else:
            initialise_onboarding_tasks.delay(user_mailbot_profile_id=profile.id)


@receiver(subscription_renewed)
def post_subscription_renewal(sender, subscription: StripeSubscription, **kwargs):
    logger.info(f"Subscription renewed for user {subscription.customer.user.email}, activating mailbot profiles")
    if subscription.price.nickname == "zapdeal":
        monthly_price = StripePrice.objects.get(nickname="mailbot_monthly")
        subscription.schedule(new_price_id=monthly_price.id)
    elif subscription.price.nickname == "freebie":
        logger.info("Free subscription renewed, canceling after 7 days")
        new_subscription = stripe.Subscription.modify(
            subscription.id, cancel_at=int((subscription.current_period_start + timezone.timedelta(days=7)).timestamp())
        )
        subscription.cancel_at = datetime.datetime.fromtimestamp(new_subscription.cancel_at, tz=datetime.timezone.utc)
        subscription.save(update_fields=("cancel_at",))
    primary_profile = subscription.customer.user.user_mailbot_profile
    activate_mailbot(primary_profile)
    secondary_mailbot_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
        user_mailbot_profile_id=primary_profile.id
    )
    secondary_mailbot_profiles = UserMailBotProfile.objects.select_related("user").filter(
        id__in=secondary_mailbot_profile_ids
    )
    for secondary_mailbot_profile in secondary_mailbot_profiles:
        activate_mailbot(secondary_mailbot_profile)
        logger.info(f"Successfully activated mailbot profiles for user {secondary_mailbot_profile.user.email}")


@receiver(signal=post_email_read)
def message_read(message: Message, **kwargs):
    """
    This receiver gets triggered if message's status changed from unread to read.
    Args:
        message: Message object which user interacted with.
    """
    if message.internal_message_category:
        logger.info(
            f"Message read for category {message.internal_message_category} by user {message.user_mailbot_profile.user.email}"
        )
        if message.internal_message_category == MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY.value:
            archive_at = timezone.now() + timezone.timedelta(
                minutes=constance_config.AUTO_ARCHIVE_DEFAULTS.get("first_time_sender_overlay_if_read", 60)
            )

            try:
                task = ScheduledTask.objects.get(
                    user=message.user_mailbot_profile.user,
                    periodic_task__task=MailBotScheduledTasks.AUTO_ARCHIVER.value,
                    periodic_task__enabled=True,
                    periodic_task__clocked__clocked_time__gt=archive_at,
                    metadata__message_id=message.message_id,
                )
                task.reschedule_one_off_task(start_time=archive_at)
                logger.info(
                    f"Updated archive timestamp of first_time_sender_overlay_mail for user {message.user_mailbot_profile.user.email} due to read action"
                )
            except ScheduledTask.DoesNotExist:
                pass
            except ScheduledTask.MultipleObjectsReturned:
                logger.exception(
                    f"Multiple tasks to archive FST overlay sent for a single message.",
                    extra={"user": message.user_mailbot_profile.user.email, "message_id": message.message_id},
                )
    elif check_feature(
        user_id=message.user_mailbot_profile.user.id,
        feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
        application_tag=ApplicationTag.MailBot,
    ):
        # Remove inbox label from archived email if it is read after a certain time
        is_lucene_email = (category_tags := message.metadata.get("category_tags")) and set(category_tags).isdisjoint(
            set(constance_config.NON_ARCHIVE_LUCENE_TAGS)
        )
        if message.generic_label == MailBotGenericLabel.WHITE_LIST.value and is_lucene_email:
            if message.user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
                filter_kwargs = {
                    "user": message.user_mailbot_profile.user,
                    "periodic_task__task": MailBotScheduledTasks.UPDATE_LABELS.value,
                    "periodic_task__enabled": True,
                    "metadata__message_id": message.message_id,
                    "metadata__remove_label_name": GmailKnownLabelName.INBOX.value,
                }
            else:
                filter_kwargs = {
                    "user": message.user_mailbot_profile.user,
                    "periodic_task__task": MailBotScheduledTasks.UPDATE_LABELS.value,
                    "periodic_task__enabled": True,
                    "metadata__message_id": message.message_id,
                    "metadata__add_label_name": OutlookKnownLabelName.ARCHIVE.value,
                }
            try:
                task = ScheduledTask.objects.get(**filter_kwargs)
            except ScheduledTask.DoesNotExist:
                logger.exception(
                    "Task to archive white_listed_mail not found for a read action.",
                    extra={"message_id": message.message_id, "user": message.user_mailbot_profile.user.email},
                )
            else:
                task.reschedule_one_off_task(
                    start_time=timezone.now()
                    + timezone.timedelta(
                        minutes=constance_config.ARCHIVING_EMAILZAP_SETTINGS["remove_inbox_after_if_read"]
                    )
                )
                logger.info(
                    "Removing inbox label from archived email due to read action",
                )


@receiver(signal=refresh_token_expired)
def post_refresh_token_expired(**kwargs):
    """
    This receiver gets triggered if refresh token is expired for any service provider.
    We will disable the mailbot, and schedule the reactivation reminder email.
    """
    if user_mailbot_profile_id := kwargs.get("user_mailbot_profile_id"):
        queryset = UserMailBotProfile.objects.select_for_update(of=("self",)).filter(id=user_mailbot_profile_id)
    else:
        logger.exception("Either user mailbot profile ID or refresh token prefix must be passed")
        return
    # Lock on the single user mailbot profile for handling concurrent requests
    with transaction.atomic():
        try:
            # Evaluation of select_for_update must be inside the atomic block
            user_mailbot_profile = queryset.get()
        except UserMailBotProfile.DoesNotExist:
            logger.exception("User mailbot profile does not exist while disabling mailbot on refresh token expiration")
        else:
            logger.info(f"Refresh token expired for {user_mailbot_profile.user.email}")
            if user_mailbot_profile.preferences.get("mailbot_enabled"):
                user_mailbot_profile.preferences["mailbot_enabled"] = False
                user_mailbot_profile.metadata[
                    MailBotProfileMetadataKey.LAST_DISABLED_AT.value
                ] = timezone.now().isoformat()
            if not user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value):
                user_mailbot_profile.metadata[MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value] = True
                user_mailbot_profile.metadata[
                    MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED_AT.value
                ] = timezone.now().isoformat()
                # Since token is expired or revoked, we cannot unsubscribe watch channel, we mark mailbot disabled separately.
                transaction.on_commit(lambda: on_refresh_token_expired.delay(user_mailbot_profile.id))
            user_mailbot_profile.save()


@receiver(click_received)
def click_handler(sender, mail_obj, click_obj, *args, **kwargs):
    """
    Triggered by SESEventWebhookView when mailbot sent email is clicked on any links.
    """
    ses_message_id = mail_obj["messageId"]
    mail_headers = index_headers(mail_obj.get("headers"))
    message_category = mail_headers.get(MailBotMessageHeaders.MESSAGE_CATEGORY.value)
    if not message_category:
        return
    sender_email = mail_headers.get("To")
    logger.info(
        f"Click event received for user {sender_email} with ses_message_id {ses_message_id} and category {message_category}"
    )
    user_mailbot_profile_id = mail_headers.get(MailBotMessageHeaders.MAILBOT_PROFILE_ID.value)
    original_message_id = mail_headers.get(MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value)
    if message_category == MailBotMessageCategory.AUTO_RESPONDER.value:
        if not sender_email:
            logger.exception(
                "Sender email must be present for identifying user who clicked auto responder verification button"
            )
            return
        if not original_message_id:
            logger.exception(
                "Original message ID must be present for identifying user's message which triggered auto responder"
            )
            return
        if not user_mailbot_profile_id:
            logger.exception("Mailbot profile ID must be present")
            return
        is_verification_link_clicked = click_obj.get("link") == AUTO_RESPONDER_MAIL_VERIFICATION_URL
        if not is_verification_link_clicked:
            logger.info(f"{sender_email} clicked on some other link than verification button in auto responder")
            return
        try:
            message = Message.objects.get(
                user_mailbot_profile_id=int(user_mailbot_profile_id), message_id=original_message_id
            )
        except Message.DoesNotExist:
            logger.exception("Message does not exist when sender responded to auto responder")
        else:
            logger.info(f"{sender_email} verified for user {message.user_mailbot_profile.user.email}")
            message.metadata["auto_responder_verified"] = True
            message.save()


@receiver(execute_jarvis_workflow_signal)
def post_jarvis_workflow_configured(sender, workflow, **kwargs):
    """
    Process instantaneous jarvis workflows with email content
    """
    # TODO : Filter by email context once changes are made
    messages = Message.objects.filter(user_mailbot_profile__user_id=workflow.user_id).order_by("-created")
    queue_jarvis_workflow_for_execution(user=workflow.user, payload=messages, workflow_id=workflow.id, _async=False)


@receiver(signal=on_mailbot_activate)
def schedule_feedback_email(sender, user_mailbot_profile_id, **kwargs):
    user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    if not user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.FEEDBACK_MAIL_SENT.value):
        lower_bound_on_start_time: datetime.datetime = max(
            user_mailbot_profile.created + timezone.timedelta(days=7), timezone.now()
        )
        # Only schedule feedback emails at 2 p.m. user's timezone
        user_timezone = pytz.timezone(user_mailbot_profile.preferences.get("digest_timezone", pytz.utc.zone))
        user_local_time = user_timezone.localize(
            timezone.datetime(
                year=lower_bound_on_start_time.year,
                month=lower_bound_on_start_time.month,
                day=lower_bound_on_start_time.day,
                hour=14,
            )
        )
        # User timezone 2 p.m. in UTC
        start_time = user_local_time.astimezone(timezone.utc)
        # If 2 p.m. is already gone, then schedule for the next day
        if start_time <= timezone.now():
            start_time += timezone.timedelta(days=1)
        try:
            task = ScheduledTask.objects.get(
                user=user_mailbot_profile.user,
                periodic_task__task=MailBotScheduledTasks.SEND_FEEDBACK_MAIL.value,
                periodic_task__enabled=True,
            )
        except ScheduledTask.DoesNotExist:
            # Create the task if not already exist
            ScheduledTask.create_one_off_task(
                user=user_mailbot_profile.user,
                start_time=start_time,
                task=MailBotScheduledTasks.SEND_FEEDBACK_MAIL.value,
                task_args=(user_mailbot_profile.user_id,),
            )
            logger.info(f"Scheduled feedback email at {start_time} for user {user_mailbot_profile.user.email}")
        else:
            # If task already exists then reschedule the task
            task.reschedule_one_off_task(start_time=start_time)
            logger.info(f"Rescheduled feedback email to {start_time} for user {user_mailbot_profile.user.email}")


@receiver(post_app_profile_delete)
def post_app_profile_deletion(sender, user, **kwargs):
    """
    Cancel user's subscription once their related app profile is deleted if not already canceled
    """
    try:
        subscription = StripeSubscription.objects.get(
            customer__user=user, status=StripeSubscription.STATUS_ACTIVE, cancel_at_period_end=False
        )
    except StripeSubscription.DoesNotExist:
        pass
    else:
        subscription.cancel(reason=StripeSubscription.CANCELED_DUE_TO_ACCOUNT_DELETION)
        logger.info(f"Subscription canceled for user {user.email} due to app profile deletion")


@receiver(feature_flag_target_users_changed)
def post_feature_flag_target_users_changed(flag: str, turned_on_for_users: set, turned_off_for_users: set, **kwargs):
    """
    Receiver to handle feature flag target users change signal
    """
    reset_launch_darkly_instance_map()
    FeatureFlagMailBotTargetChanged(
        flag=flag, turned_on_for_users=turned_on_for_users, turned_off_for_users=turned_off_for_users
    ).handle()


@receiver(pre_delete, sender=UserMailBotProfile)
def pre_delete_user_mailbot_profile(sender, instance: UserMailBotProfile, **kwargs):
    """
    Deactivate mailbot before deleting the mailbot profile
    """
    deactivate_mailbot(instance)
    if settings.APP_ENV != APP_ENV_PROD:
        # Deleting the customer will delete all the associated data with the customer from stripe automatically
        for customer in stripe.Customer.search(query=f'email:"{instance.user.email}"').auto_paging_iter():
            logger.info(f"Deleting stripe customer {customer.id} for user {instance.user.email}")
            try:
                customer.delete()
            except InvalidRequestError:
                logger.info(f"Failed to deleted customer {customer.id} for user {instance.user.email}")
                continue


@receiver(scan_completed)
def handle_scan_completed(sender, profile_id, message_ids, **kwargs):
    """
    Handles the scan_completed signal by triggering the background analysis task.
    """
    if not message_ids:
        logger.info(f"No messages to analyze for profile {profile_id}. Skipping task.")
        return

    logger.info(
        f"Received scan_completed signal for profile {profile_id}. Triggering analysis for {len(message_ids)} messages."
    )
    analyze_messages_in_chunks(profile_id, message_ids)


@receiver(post_label_change)
def handle_post_label_change(sender, message, old_generic_label, new_generic_label, **kwargs):
    """
    Receiver for post_label_change signal.

    Args:
        sender: The class that sent the signal
        message: The message object whose label changed
        old_generic_label: The previous generic label
        new_generic_label: The new generic label
        **kwargs: Additional keyword arguments
    """
    # If the message is a forwarded one, update the label on the original message in the secondary account
    if message.internal_message_category in FORWARDED_OVERLAY_MESSAGE_CATEGORIES:
        logger.info(
            f"Handling label change for forwarded message {message.message_id} from {old_generic_label} to {new_generic_label}"
        )
        original_message_id = message.fetch_header(MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value)

        if not original_message_id:
            logger.warning(f"[LabelSync] No original_message_id found for forwarded message {message.message_id}")
            return

        try:
            original_message = Message.objects.get(message_id=original_message_id)
        except Message.DoesNotExist:
            logger.warning(
                f"[LabelSync] Original message {original_message_id} not found for forwarded message {message.message_id}"
            )
        else:
            SecondaryAccountEmailHandler.handle_label_change(
                forwarded_message=message,
                original_message=original_message,
                remove_label_name=old_generic_label,
                add_label_name=new_generic_label,
            )
