"""
Tests for the enhanced rule engine system.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock

from mailbot.rules.engine import <PERSON>hanced<PERSON><PERSON>E<PERSON><PERSON>, get_enhanced_rule_engine
from mailbot.rules.loader import RuleSetLoader
from mailbot.rules.factory import RuleFactory, FilterFactory, ActionFactory
from mailbot.rules.filters import Score<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Category<PERSON><PERSON><PERSON>, ReadFractionFilter
from mailbot.rules.actions import ParameterizedSetLabelAction, ParameterizedDefaultAction
from mailbot.utils.message_parser import ParsedMessage
from mailbot.models import MailBotGenericLabel, MessageCategory


class TestEnhancedRuleEngine:
    """Test cases for EnhancedRuleEngine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = EnhancedRuleEngine()
        self.mock_message = Mock(spec=ParsedMessage)
        self.mock_message.message_id = "test_message_123"
        self.mock_message.user_mailbot_profile_id = 1
    
    def test_engine_initialization(self):
        """Test engine initialization."""
        assert self.engine.rule_loader is not None
        assert self.engine.rule_factory is not None
        assert isinstance(self.engine._rule_cache, dict)
    
    @patch('mailbot.rules.engine.get_rule_loader')
    def test_process_message_with_rules(self, mock_get_loader):
        """Test processing message with loaded rules."""
        # Mock rule loader
        mock_loader = Mock()
        mock_rule = Mock()
        mock_rule.priority = 1
        mock_rule.filter_expression = Mock()
        mock_rule.filter_expression.evaluate.return_value = {"match": True}
        mock_rule.actions = [Mock()]
        mock_rule.actions[0].execute.return_value = {
            "label_name": "INBOX",
            "message_labelled_due_to": "TEST_RULE"
        }
        
        mock_loader.load_rule_set.return_value = [mock_rule]
        mock_get_loader.return_value = mock_loader
        
        # Process message
        result = self.engine.process_message("v3", self.mock_message)
        
        # Verify result
        assert result["label_name"] == "INBOX"
        assert result["message_labelled_due_to"] == "TEST_RULE"
        mock_loader.load_rule_set.assert_called_once_with("v3", "main")
    
    def test_process_message_no_rules(self):
        """Test processing message when no rules are found."""
        with patch.object(self.engine, '_get_rules', return_value=[]):
            result = self.engine.process_message("v3", self.mock_message)
        
        # Should return default result
        assert result["label_name"] == MailBotGenericLabel.ZAPPED.value
        assert "message_labelled_due_to" in result
    
    def test_rule_caching(self):
        """Test that rules are cached properly."""
        with patch.object(self.engine.rule_loader, 'load_rule_set') as mock_load:
            mock_load.return_value = []
            
            # First call should load rules
            self.engine._get_rules("v3", "main")
            assert mock_load.call_count == 1
            
            # Second call should use cache
            self.engine._get_rules("v3", "main")
            assert mock_load.call_count == 1
    
    def test_clear_cache(self):
        """Test clearing rule cache."""
        # Add something to cache
        self.engine._rule_cache["test"] = []
        assert len(self.engine._rule_cache) > 0
        
        # Clear cache
        self.engine.clear_cache()
        assert len(self.engine._rule_cache) == 0
    
    def test_reload_rules(self):
        """Test reloading specific rules."""
        # Add to cache
        self.engine._rule_cache["v3:main"] = []
        self.engine._rule_cache["v2:main"] = []
        
        # Reload specific rule set
        self.engine.reload_rules("v3", "main")
        
        # Only v3:main should be removed
        assert "v3:main" not in self.engine._rule_cache
        assert "v2:main" in self.engine._rule_cache
    
    def test_validate_rules(self):
        """Test rule validation."""
        mock_rule = Mock()
        mock_rule.priority = 1
        mock_rule.filter_expression = Mock()
        mock_rule.actions = [Mock()]
        
        with patch.object(self.engine, '_get_rules', return_value=[mock_rule]):
            result = self.engine.validate_rules("v3", "main")
        
        assert result["valid"] is True
        assert result["rule_count"] == 1
        assert len(result["errors"]) == 0
    
    def test_get_rule_info(self):
        """Test getting rule information."""
        mock_rule = Mock()
        mock_rule.priority = 1
        mock_rule.filter_expression.__class__.__name__ = "TestFilter"
        mock_rule.actions = [Mock(), Mock()]
        mock_rule.actions[0].__class__.__name__ = "TestAction1"
        mock_rule.actions[1].__class__.__name__ = "TestAction2"
        
        with patch.object(self.engine, '_get_rules', return_value=[mock_rule]):
            result = self.engine.get_rule_info("v3", "main")
        
        assert result["algorithm_version"] == "v3"
        assert result["rule_set_name"] == "main"
        assert result["rule_count"] == 1
        assert len(result["rules"]) == 1
        assert result["rules"][0]["priority"] == 1
        assert result["rules"][0]["action_count"] == 2


class TestRuleSetLoader:
    """Test cases for RuleSetLoader."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.loader = RuleSetLoader()
    
    def test_loader_initialization(self):
        """Test loader initialization."""
        assert self.loader.rules_dir is not None
        assert self.loader.environment is not None
        assert self.loader.rule_factory is not None
    
    @patch('builtins.open')
    @patch('yaml.safe_load')
    @patch('pathlib.Path.exists')
    def test_load_rule_set(self, mock_exists, mock_yaml_load, mock_open):
        """Test loading rule set from YAML."""
        # Mock file existence
        mock_exists.return_value = True
        
        # Mock YAML content
        mock_yaml_load.return_value = {
            "rule_sets": {
                "main": [
                    {
                        "priority": 1,
                        "filters": [{"type": "AllFilter"}],
                        "actions": [{"type": "ParameterizedSetLabelAction"}]
                    }
                ]
            }
        }
        
        # Mock rule creation
        with patch.object(self.loader, '_create_rule_from_config') as mock_create:
            mock_rule = Mock()
            mock_create.return_value = mock_rule
            
            rules = self.loader.load_rule_set("v3", "main")
            
            assert len(rules) == 1
            assert rules[0] == mock_rule
            mock_create.assert_called_once()


class TestFilterFactory:
    """Test cases for FilterFactory."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.factory = FilterFactory()
    
    def test_factory_initialization(self):
        """Test factory initialization."""
        assert len(self.factory._filter_classes) > 0
        assert "AllFilter" in self.factory._filter_classes
    
    def test_create_simple_filter(self):
        """Test creating simple filter."""
        # Register a test filter
        class TestFilter:
            def __init__(self, **kwargs):
                self.params = kwargs
        
        self.factory.register_filter("TestFilter", TestFilter)
        
        config = {
            "type": "TestFilter",
            "params": {"test_param": "test_value"}
        }
        
        filter_obj = self.factory.create_filter(config)
        assert isinstance(filter_obj, TestFilter)
        assert filter_obj.params["test_param"] == "test_value"
    
    def test_create_composite_filter(self):
        """Test creating composite filter (AllFilter)."""
        config = {
            "type": "AllFilter",
            "filters": [
                {"type": "AllFilter", "filters": []},
                {"type": "AllFilter", "filters": []}
            ]
        }
        
        with patch.object(self.factory, '_filter_classes') as mock_classes:
            mock_all_filter = Mock()
            mock_classes.__getitem__.return_value = mock_all_filter
            mock_classes.__contains__.return_value = True
            
            filter_obj = self.factory.create_filter(config)
            
            # Should create AllFilter with sub-filters
            mock_all_filter.assert_called()
    
    def test_unknown_filter_type(self):
        """Test creating unknown filter type raises error."""
        config = {"type": "UnknownFilter"}
        
        with pytest.raises(ValueError, match="Unknown filter type"):
            self.factory.create_filter(config)


class TestActionFactory:
    """Test cases for ActionFactory."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.factory = ActionFactory()
    
    def test_factory_initialization(self):
        """Test factory initialization."""
        assert len(self.factory._action_classes) > 0
    
    def test_create_parameterized_action(self):
        """Test creating parameterized action."""
        config = {
            "type": "ParameterizedSetLabelAction",
            "params": {
                "label_name": "TEST_LABEL",
                "reason": "TEST_REASON"
            }
        }
        
        # Mock the action class
        with patch.object(self.factory, '_action_classes') as mock_classes:
            mock_action_class = Mock()
            mock_action_instance = Mock()
            mock_action_class.return_value = mock_action_instance
            mock_classes.__getitem__.return_value = mock_action_class
            mock_classes.__contains__.return_value = True
            
            action = self.factory.create_action(config)
            
            mock_action_class.assert_called_once_with(
                label_name="TEST_LABEL",
                reason="TEST_REASON"
            )
            assert action == mock_action_instance


class TestEnhancedFilters:
    """Test cases for enhanced filter classes."""
    
    def test_score_threshold_filter(self):
        """Test ScoreThresholdFilter."""
        filter_obj = ScoreThresholdFilter(threshold=2.0, operator=">=")
        
        # Test with matching score
        result = filter_obj.evaluate(None, score=3.0)
        assert result["match"] is True
        assert result["score"] == 3.0
        
        # Test with non-matching score
        result = filter_obj.evaluate(None, score=1.0)
        assert result["match"] is False
    
    def test_category_filter(self):
        """Test CategoryFilter."""
        filter_obj = CategoryFilter(category_names=["Test Category"])
        
        # Mock categories
        mock_category = Mock()
        mock_category.name = "Test Category"
        
        result = filter_obj.evaluate(None, message_categories=[mock_category])
        assert result["match"] is True
        assert "Test Category" in result["matches"]
    
    def test_read_fraction_filter(self):
        """Test ReadFractionFilter."""
        filter_obj = ReadFractionFilter(threshold=0.5, operator=">=")
        
        # Mock sender profile
        mock_profile = Mock()
        mock_profile.read_count = 7
        mock_profile.total_count = 10
        
        result = filter_obj.evaluate(None, sender_profile=mock_profile)
        assert result["match"] is True
        assert result["read_fraction"] == 0.7


class TestEnhancedActions:
    """Test cases for enhanced action classes."""
    
    def test_parameterized_set_label_action(self):
        """Test ParameterizedSetLabelAction."""
        action = ParameterizedSetLabelAction(
            label_name="TEST_LABEL",
            reason="TEST_REASON",
            include_in_digest=True
        )
        
        mock_message = Mock()
        filter_result = {}
        
        result = action.execute(mock_message, filter_result, action_instance=action)
        
        assert result["label_name"] == "TEST_LABEL"
        assert result["message_labelled_due_to"] == "TEST_REASON"
        assert result["include_in_digest"] is True
    
    def test_parameterized_default_action(self):
        """Test ParameterizedDefaultAction."""
        action = ParameterizedDefaultAction(
            default_label="DEFAULT_LABEL",
            default_reason="DEFAULT_REASON",
            confidence=0.8
        )
        
        mock_message = Mock()
        filter_result = {}
        
        result = action.execute(mock_message, filter_result, action_instance=action)
        
        assert result["label_name"] == "DEFAULT_LABEL"
        assert result["message_labelled_due_to"] == "DEFAULT_REASON"
        assert result["confidence"] == 0.8


class TestGlobalFunctions:
    """Test cases for global functions."""
    
    def test_get_enhanced_rule_engine(self):
        """Test getting global enhanced rule engine."""
        engine1 = get_enhanced_rule_engine()
        engine2 = get_enhanced_rule_engine()
        
        assert engine1 is engine2
        assert isinstance(engine1, EnhancedRuleEngine)
